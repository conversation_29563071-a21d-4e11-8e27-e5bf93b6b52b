<template>
    <div class="maintenance-defect-detail">
        <div class="flex">
            <h3>订单详情</h3>
            <div>
                <el-button
                    v-permission="['projectOrderUpdate']"
                    type="primary"
                    @click="handleEdit"
                    >编辑</el-button
                >
                <el-button type="primary" @click="goBack">返回</el-button>
            </div>
        </div>

        <!-- 基本信息 -->
        <el-divider>基本信息</el-divider>
        <el-descriptions :column="4" border>
            <el-descriptions-item label="客户名称" :span="2">{{
                form.customerName
            }}</el-descriptions-item>
            <el-descriptions-item label="项目经理" :span="2">{{
                form.projectManagerName
            }}</el-descriptions-item>
            <el-descriptions-item label="OA流程状态" :span="2">{{
                form.flowStatus
            }}</el-descriptions-item>
            <el-descriptions-item label="OA流程" :span="2">{{
                form.flowNo
            }}</el-descriptions-item>
            <el-descriptions-item label="产品型号">{{
                form.productModel
            }}</el-descriptions-item>
            <el-descriptions-item label="产品名称">{{
                form.productName
            }}</el-descriptions-item>
            <el-descriptions-item label="产品线">{{
                form.productLine
            }}</el-descriptions-item>
            <el-descriptions-item label="细分产品线">{{
                form.subProductLine
            }}</el-descriptions-item>
        </el-descriptions>

        <el-divider>订单识别</el-divider>
        <el-descriptions :column="5" border class="mt-20">
            <el-descriptions-item label="订单类型">{{
                form.orderType
            }}</el-descriptions-item>
            <el-descriptions-item label="产品类型">{{
                form.productType
            }}</el-descriptions-item>
            <el-descriptions-item label="订单生产类型">{{
                form.orderProductType
            }}</el-descriptions-item>
            <el-descriptions-item label="是否使用库存整改">{{
                form.whetherInventoryRectify
            }}</el-descriptions-item>
            <el-descriptions-item label="生产单位">{{
                form.productUnit
            }}</el-descriptions-item>
        </el-descriptions>
        <el-table
            :data="form.orderRequireList"
            border
            class="base-config-table"
        >
            <el-table-column
                header-align="center"
                prop="baseConfigRequire"
                label="基本配置要求"
            ></el-table-column>
            <el-table-column
                align="center"
                header-align="center"
                prop="orderCount"
                label="数量"
                width="80"
            ></el-table-column>
            <el-table-column
                align="center"
                header-align="center"
                prop="requireDate"
                label="要求供货日期"
                width="150"
            ></el-table-column>
        </el-table>

        <template v-if="form.proProjectId">
            <el-divider>订单应对计划</el-divider>
            <ZentaoTaskList
                class="mt-10"
                :show="isShowZentaoTaskList"
                :proProjectId="form.proProjectId"
                :proTaskId="form.proTaskId"
                :projectManagerAccount="projectManagerAccount"
            ></ZentaoTaskList>
        </template>
        <div v-show="form.whetherExistRisk === '是'">
            <el-divider>订单需求应对的风险</el-divider>
            <RiskList
                class="mt-10"
                :riskListCounter="riskListCounter"
                :assObjectId="id"
                relatedObject="维护项目订单"
            ></RiskList>
        </div>

        <OrderUpdateDialog
            :visible.sync="orderUpdateDialogVisible"
            :id="id"
            @success="getDetail"
        ></OrderUpdateDialog>
    </div>
</template>

<script>
import ZentaoTaskList from 'maintenanceProject/components/ZentaoTaskList';
import RiskList from 'maintenanceProject/components/RiskList';
import OrderUpdateDialog from '../OrderList/OrderUpdateDialog';

export default {
    name: 'MaintenanceDefectDetail',
    components: {
        ZentaoTaskList,
        RiskList,
        OrderUpdateDialog
    },
    data() {
        return {
            form: {
                customerName: '',
                flowNo: '',
                flowStatus: '',
                orderPlanType: '',
                orderProductType: '',
                orderRequire: '',
                orderRequireList: [],
                orderType: '',
                proProjectId: 0,
                proTaskId: 0,
                productLine: '',
                productModel: '',
                productName: '',
                productType: '',
                productUnit: '',
                projectManager: '',
                projectManagerName: '',
                subProductLine: '',
                whetherExistRisk: '',
                whetherInventoryRectify: ''
            },
            orderUpdateDialogVisible: false,
            id: '',
            isShowZentaoTaskList: false,
            projectManagerAccount: '',
            riskListCounter: 0
        };
    },
    created() {
        const { query = null } = this.$route;

        if (query) {
            this.id = query.order_id;
            this.getDetail();
        }
        this.closeAllPopovers();
    },
    methods: {
        goBack() {
            this.$router.go(-1);
        },
        handleEdit() {
            this.orderUpdateDialogVisible = true;
        },
        /**
         * 获取订单详情
         */
        async getDetail() {
            if (!this.id) return;
            const api = this.$service.maintenanceProject.order.getDetail;
            const params = {
                orderId: this.id,
                // 流程软硬件归属，固定为硬件
                flowSoftHardBelong: '硬件'
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.form = res.body;
                    this.projectManagerAccount = this.form.projectManager;
                    this.isShowZentaoTaskList = !!this.form.proProjectId;
                    if (this.form.whetherExistRisk === '是') {
                        this.riskListCounter += 1;
                    }
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 关闭所有弹窗
         */
        closeAllPopovers() {
            this.$root.$emit('close-popovers');

            const popovers = document.querySelectorAll('.el-popover');
            popovers.forEach((popover) => {
                if (popover.style.display !== 'none') {
                    document.body.click();
                    popover.style.display = 'none';
                }
            });

            const tooltipPopovers = document.querySelectorAll(
                '.el-tooltip__popper'
            );
            tooltipPopovers.forEach((tooltipPopover) => {
                if (tooltipPopover.style.display !== 'none') {
                    document.body.click();
                    tooltipPopover.style.display = 'none';
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.maintenance-defect-detail {
    padding: 20px;
    height: calc(100vh - 20px);
    overflow: auto;
}
.flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.mt-10 {
    margin-top: 10px;
}
.mt-20 {
    margin-top: 20px;
}
.base-config-table {
    margin-top: 20px;
    border: 1px solid #8c8c8c !important;
}
::v-deep .el-descriptions-item__label {
    width: 120px;
}
::v-deep .el-table .el-table__row {
    height: auto !important;
}
::v-deep .el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border-right: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border: 1px solid #8c8c8c !important;
}
.pre-wrap-text {
    white-space: pre-wrap;
}
</style>
