// 目前框架路由，只支持到二级路由
// 一体两翼的二级页面放到这个路由文件下了，因为针对一体两翼的一级页面也要做权限控制
// 而一体两翼的一级页面位于首页，进行权限控制的影响范围广，所以需要找一个一级页面附属
export default [
    {
        path: '/bizConfig',
        redirect: 'noRedirect',
        name: 'BizConfig',
        useLayout: true,
        meta: {
            title: '业务配置'
        },
        children: [
            {
                path: 'productLine',
                component: () => import('../views/productLine'),
                name: 'ProductLine',
                meta: {
                    title: '产品线总监配置',
                    icon: 'el-icon-s-data'
                }
            },
            {
                path: 'privilege',
                component: () => import('../views/privilege'),
                name: 'Privilege',
                meta: {
                    title: '项目白名单',
                    icon: 'el-icon-paperclip'
                }
            },
            {
                path: 'director',
                component: () => import('../views/director'),
                name: 'Director',
                meta: {
                    title: '部门负责人配置',
                    icon: 'el-icon-date'
                }
            },

            {
                path: 'workCalendar',
                component: () => import('../views/workCalendar'),
                name: 'WorkCalendar',
                meta: {
                    title: '工作日配置',
                    icon: 'el-icon-date'
                }
            },
            {
                path: 'group',
                component: () => import('../views/group'),
                name: 'Group',
                meta: {
                    title: '小组管理',
                    icon: 'el-icon-user-solid'
                }
            },
            {
                path: 'dataExport',
                component: () => import('../views/dataExport'),
                name: 'DataExport',
                meta: {
                    title: '数据导出'
                }
            },
            {
                path: 'finance-scene',
                name: 'FinanceScene',
                hidden: true,
                component: () =>
                    import('scene/views/scene/finance-scene/index.vue'),
                meta: { title: '金融机具', icon: 'fa el-icon-document' }
            },
            {
                path: 'key-basic-components',
                name: 'KeyBasicComponents',
                hidden: true,
                component: () =>
                    import('scene/views/scene/key-basic-components/index.vue'),
                meta: { title: '关键基础零部件', icon: 'fa el-icon-document' }
            },
            {
                path: 'dedicated-printer',
                name: 'DedicatedPrinter',
                hidden: true,
                component: () =>
                    import('scene/views/scene/dedicated-printer/index.vue'),
                meta: { title: '专用打印机', icon: 'fa el-icon-document' }
            },
            {
                path: 'smart-terminal',
                name: 'SmartTerminal',
                hidden: true,
                component: () =>
                    import('scene/views/scene/smart-terminal/index.vue'),
                meta: { title: '智能自助终端', icon: 'fa el-icon-document' }
            },
            {
                path: 'sorting-system',
                name: 'SortingSystem',
                hidden: true,
                component: () =>
                    import('scene/views/scene/sorting-system/index.vue'),
                meta: { title: '物流分拣', icon: 'fa el-icon-document' }
            },
            {
                path: 'privilege',
                component: () => import('../views/privilege'),
                name: 'Privilege',
                meta: {
                    title: '项目白名单',
                    icon: 'el-icon-paperclip'
                }
            },
            {
                path: 'productLine',
                component: () => import('../views/productLine'),
                name: 'ProductLine',
                meta: {
                    title: '产品线总监配置',
                    icon: 'el-icon-s-data'
                }
            }
        ]
    }
];
