<template>
    <div class="background">
        <div class="left">
            <div class="left-button">
                <div class="top-button">关键零部件</div>
                <div
                    class="mid-button"
                    @click="goToPage('key-basic-components')"
                >
                    <img
                        :src="keyBasicComponents"
                        alt="关键基础零部件"
                        style="width: 2vw; height: 4vh"
                    />关键基础零部件
                </div>
            </div>
            <img
                :src="leftSlide"
                alt="背景"
                style="width: 50%; transform: scale(0.8)"
            />
        </div>
        <div class="middle"><Topic /></div>
        <div class="right">
            <img
                :src="rightSlide"
                alt="背景"
                style="width: 50%; transform: scale(0.8)"
            />
            <div class="left-button">
                <div class="top-button">服务/运营</div>
                <div
                    class="mid-button"
                    style="margin-top: 6.5vh; margin-right: 195%; width: 120%"
                >
                    <img
                        :src="productMaintenance"
                        alt="产品维保服务"
                        style="width: 2vw; height: 4vh"
                    />产品维保服务
                </div>
                <div
                    class="mid-button"
                    style="margin-top: 12.5vh; margin-right: 205%; width: 140%"
                >
                    <img
                        :src="newRetail"
                        alt="新零售综合运营"
                        style="width: 2vw; height: 4vh; margin-right: 0.2vw"
                    />新零售综合运营
                </div>
                <div
                    class="mid-button"
                    style="margin-top: 12vh; margin-right: 160%; width: 160%"
                >
                    <img
                        :src="logisticsSortingIcon"
                        alt="物流自动化分拣运营"
                        style="width: 2vw; height: 4vh; margin-right: 0.2vw"
                    />物流自动化分拣运营
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import Topic from './Topic.vue';
import leftSlide from 'scene/assets/businessSector/left-slide.png';
import rightSlide from 'scene/assets/businessSector/right-slide.png';
import keyBasicComponents from 'scene/assets/businessSector/key-basic-components.png';
import productMaintenance from 'scene/assets/businessSector/product-maintenance.png';
import newRetail from 'scene/assets/businessSector/new-retail.png';
import logisticsSortingIcon from 'scene/assets/businessSector/logistics-sorting-icon.png';
import logisticsSorting from 'scene/assets/businessSector/logistics-sorting.png';

export default {
    name: 'BusinessSector',
    components: {
        Topic
    },
    data() {
        return {
            leftSlide,
            rightSlide,
            keyBasicComponents,
            productMaintenance,
            newRetail,
            logisticsSortingIcon,
            logisticsSorting
        };
    },
    methods: {
        goToPage(path) {
            this.$router.push({
                path: `/bizConfig/${path}`
            });
        }
    }
};
</script>
<style lang="scss" scoped>
.background {
    width: 100%;
    height: calc(100vh - 100px);
    background-image: url('~scene/assets/businessSector/base-background.png');
    background-size: cover;
    background-position: center;
    display: flex;
}
.left {
    width: 25%;
    height: 78%;
    display: flex;
    margin: auto 0;
    .top-button {
        background-color: #f6d5aa;
        background: linear-gradient(135deg, #f6d5aa, #e7b27c);
        border-radius: 99rem;
        height: 6%;
        width: 80%;
        color: #956742;
        font-size: 1.1vw;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 80%;
    }
    .left-button {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        height: 100%;
    }
    .mid-button {
        width: 130%;
        height: 10%;
        background-image: url('~scene/assets/businessSector/button-background.png');
        background-size: cover;
        background-position: center;
        border-radius: 99rem;
        margin-left: 80%;
        font-size: 1.4vw;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-top: 26.5vh;
        z-index: 2;
        cursor: pointer;
        &:hover {
            transform: scale(1.1);
        }
    }
}
.middle {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}
.right {
    width: 25%;
    height: 78%;
    display: flex;
    margin: auto 0;
    .top-button {
        background-color: #f6d5aa;
        background: linear-gradient(135deg, #f6d5aa, #e7b27c);
        border-radius: 99rem;
        height: 6%;
        width: 80%;
        color: #956742;
        font-size: 1.1vw;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 80%;
    }
    .left-button {
        display: flex;
        flex-direction: column;
        align-items: center;
        flex: 1;
        height: 100%;
    }
    .mid-button {
        height: 10%;
        background-image: url('~scene/assets/businessSector/button-background.png');
        background-size: cover;
        background-position: center;
        border-radius: 99rem;
        margin-left: 80%;
        font-size: 1.4vw;
        font-weight: 400;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}
</style>
