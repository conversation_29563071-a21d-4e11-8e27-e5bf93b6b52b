<template>
    <div class="last-week-progress-and-plan">
        <div class="title-container">
            <div class="title">上周进展与本周计划</div>
            <el-button v-if="isProjectMember" type="text" @click="handleEdit"
                >编辑</el-button
            >
        </div>
        <el-table
            :data="list"
            style="width: 100%"
            :span-method="objectSpanMethod"
        >
            <!-- 模块 -->
            <el-table-column
                prop="module"
                label="模块"
                align="center"
                width="80"
            ></el-table-column>

            <!-- 任务名称 -->
            <el-table-column
                prop="taskName"
                label="任务名称"
                header-align="center"
                align="left"
                width="350"
            ></el-table-column>

            <!-- 计划完成时间 -->
            <el-table-column
                prop="planFinishDate"
                label="计划完成时间"
                align="center"
                width="100"
            ></el-table-column>

            <!-- 责任人 -->
            <el-table-column
                prop="responsiblePersonName"
                label="责任人"
                align="center"
                width="90"
            ></el-table-column>

            <!-- 上周进展 -->
            <el-table-column label="上周进展" align="center">
                <el-table-column
                    prop="lastWeekProgress"
                    label="上周进度"
                    align="center"
                    width="80"
                >
                    <template slot-scope="scope">
                        {{
                            scope.row.lastWeekProgress !== null
                                ? `${scope.row.lastWeekProgress}%`
                                : ''
                        }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="progressSupplement"
                    label="进展补充"
                    header-align="center"
                    align="left"
                >
                    <template slot-scope="scope">
                        <span class="pre-line">
                            {{ scope.row.progressSupplement }}</span
                        >
                    </template>
                </el-table-column>
            </el-table-column>

            <!-- 本周计划 -->
            <el-table-column label="本周计划" align="center">
                <el-table-column
                    prop="thisWeekProgress"
                    label="本周进度"
                    align="center"
                    width="80"
                >
                    <template slot-scope="scope">
                        {{
                            scope.row.thisWeekProgress !== null
                                ? `${scope.row.thisWeekProgress}%`
                                : ''
                        }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="planSupplement"
                    label="计划补充"
                    header-align="center"
                    align="left"
                >
                    <template slot-scope="scope">
                        <span class="pre-line">
                            {{ scope.row.planSupplement }}</span
                        >
                    </template>
                </el-table-column>
            </el-table-column>
        </el-table>
        <EditDialog
            :visible.sync="editDialogVisible"
            :isProjectManagerOrPQA="isProjectManagerOrPQA"
            :projectMembersOptions="projectMembersOptions"
            :weeklyId="weeklyId"
            @success="handleSuccess"
        />
    </div>
</template>

<script>
import EditDialog from './EditDialog.vue';

export default {
    name: 'LastWeekProgressAndPlan',
    components: {
        EditDialog
    },
    props: {
        isProjectManagerOrPQA: {
            type: Boolean,
            default: false
        },
        projectMembers: {
            type: Array,
            default: () => []
        },
        isProjectMember: {
            type: Boolean,
            default: false
        },
        list: {
            type: Array,
            default: () => []
        },
        weeklyId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            editDialogVisible: false
        };
    },
    computed: {
        // 从所有人员中筛选出项目成员
        projectMembersOptions() {
            const allPersonData = this.$store.state.project.currentEmployeeList;

            const projectMembers = allPersonData.filter((i) =>
                this.projectMembers.includes(i.loginName)
            );
            return projectMembers;
        }
    },
    watch: {},
    methods: {
        /**
         * 编辑
         */
        handleEdit() {
            this.editDialogVisible = true;
        },
        /**
         * 保存成功
         */
        handleSuccess() {
            this.$emit('success');
        },
        /**
         * 合并单元格
         * @param {Object} row 行数据
         * @param {Object} column 列数据
         * @param {Number} rowIndex 行索引
         * @param {Number} columnIndex 列索引
         * @returns {Object} 合并单元格
         */
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            // 只在“模块”这一列进行合并, 其他保持现状
            if (columnIndex !== 0) {
                return {
                    rowspan: 1,
                    colspan: 1
                };
            }
            // 如果当前行的“项”与上一行的“项”相同，则合并
            if (rowIndex > 0 && row.module === this.list[rowIndex - 1].module) {
                return {
                    // 隐藏当前行的单元格
                    rowspan: 0,
                    colspan: 0
                };
            }
            // 计算当前“项”需要合并的行数
            let rowspan = 1;
            for (let i = rowIndex + 1; i < this.list.length; i++) {
                if (row.module === this.list[i].module) {
                    rowspan += 1;
                } else {
                    break;
                }
            }
            return {
                rowspan,
                colspan: 1
            };
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'project/views/projectReport/components/common/common.scss';

.title-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #8c8c8c;
    height: 28px;
}
.title {
    @include section-title;
}
.pre-line {
    white-space: pre-line;
}
</style>
