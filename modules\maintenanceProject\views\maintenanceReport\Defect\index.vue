<template>
    <div class="defect-container">
        <Header @get-table-list="getTableData" pageType="defect">
            <template #customButton>
                <el-button
                    v-show="
                        projectStore.length === 3 &&
                        (isProjectManager || isPqa) &&
                        isEditable
                    "
                    class="select-defect-button"
                    @click="handleSelectDefect"
                    type="text"
                >
                    选择缺陷
                </el-button>
            </template>
        </Header>
        <el-table
            :data="tableData"
            style="width: 100%"
            :cell-style="{ verticalAlign: 'top' }"
            height="calc(100vh - 180px)"
        >
            <el-table-column
                prop="productModel"
                label="产品型号"
                header-align="center"
                width="100"
            >
                <template slot-scope="scope">
                    <div class="product-model-container">
                        <div>{{ scope.row.productModel }}</div>
                        <br />
                        <div class="project-manager-container">项目经理</div>
                        <div>{{ scope.row.projectManagerName }}</div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                header-align="center"
                align="left"
                label="缺陷描述"
                min-width="280"
            >
                <template slot-scope="scope">
                    <div class="defect-desc-container">
                        <div class="defect-desc-title">
                            <b>缺陷来源：</b> {{ scope.row.faultSource }}
                        </div>
                        <div
                            class="defect-desc-title"
                            style="margin-bottom: 10px"
                        >
                            <b> 缺陷关联客户：</b>{{ scope.row.customerName }}
                        </div>
                        <div class="defect-desc-title">
                            <b>缺陷描述：</b>
                            <div class="pre-line">
                                {{ scope.row.faultOverview }}
                            </div>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                prop="faultLevel"
                label="严重等级"
                header-align="center"
                align="center"
                width="70"
            ></el-table-column>
            <el-table-column
                prop="faultStatus"
                label="缺陷状态"
                header-align="center"
                align="center"
                width="70"
            ></el-table-column>
            <el-table-column label="时间" width="110" header-align="center">
                <template slot-scope="scope">
                    <div class="deadline-container">
                        <div>发现日期</div>
                        <div>{{ scope.row.faultFindDate }}</div>
                        <div>持续天数</div>
                        <div style="margin-bottom: 10px">
                            {{ scope.row.daysPending }}
                        </div>
                        <div>计划完成日期</div>
                        <div>{{ scope.row.planFinishDate }}</div>
                    </div>
                </template>
            </el-table-column>
            <!-- TODO:  看看 -->
            <el-table-column
                label="技术根因分析"
                header-align="center"
                align="left"
                width="120"
            >
                <template slot-scope="scope">
                    <div>
                        <b>技术委员会</b>
                        <div>
                            {{
                                scope.row.whetherAscendTechCommittee === '是'
                                    ? '已上升'
                                    : '未上升'
                            }}
                        </div>
                        <b>原因分析</b>
                        <div>{{ scope.row.causeAnalyse }}</div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                label="临时措施及响应计划"
                min-width="250"
                header-align="center"
            >
                <template slot-scope="scope">
                    <div class="response-plan-container">
                        <b>【临时措施】：</b>
                        <div>{{ scope.row.solutionMeasuresTemp }}</div>
                        <b>【响应计划】：</b>
                        <div
                            v-for="(item, itemIndex) in scope.row
                                .planProgressTempList"
                            :key="itemIndex"
                            class="plan-item"
                        >
                            <div
                                :class="[
                                    'plan-item-content',
                                    {
                                        clickable: isClickable(item)
                                    }
                                ]"
                                @click="
                                    isClickable(item)
                                        ? handlePlanItemClick(item)
                                        : null
                                "
                            >
                                ● {{ item.deadline }}，{{ item.taskName }}（{{
                                    item.responsiblePerson
                                }}），{{
                                    item.taskProgress
                                        ? `${item.taskProgress}%`
                                        : '未启动'
                                }}
                            </div>
                            <div v-if="item.taskNote" class="taskNote">
                                进展补充：{{ item.taskNote }}
                            </div>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                label="永久措施及响应计划"
                min-width="300"
                header-align="center"
            >
                <template slot-scope="scope">
                    <div class="response-plan-container">
                        <b>【永久措施】：</b>
                        <div>{{ scope.row.solutionMeasures }}</div>
                        <b>【响应计划】：</b>
                        <div
                            v-for="(item, itemIndex) in scope.row
                                .planProgressList"
                            :key="itemIndex"
                        >
                            <div
                                :class="[
                                    'plan-item-content',
                                    {
                                        clickable: isClickable(item)
                                    }
                                ]"
                                @click="
                                    isClickable(item)
                                        ? handlePlanItemClick(item)
                                        : null
                                "
                            >
                                ● {{ item.deadline }}，{{ item.taskName }}（{{
                                    item.responsiblePerson
                                }}），{{
                                    item.taskProgress
                                        ? `${item.taskProgress}%`
                                        : '未启动'
                                }}
                            </div>
                            <div v-if="item.taskNote" class="taskNote">
                                进展补充：{{ item.taskNote }}
                            </div>
                        </div>
                    </div>
                </template>
            </el-table-column>
            <el-table-column
                label="风险及需支持事项"
                min-width="300"
                header-align="center"
            >
                <template slot-scope="scope">
                    <div
                        v-if="
                            scope.row.riskList && scope.row.riskList.length > 0
                        "
                    >
                        <div
                            v-for="(item, index) in scope.row.riskList"
                            :key="index"
                            class="riskDesc-support-container"
                        >
                            <div v-if="item.riskDesc">
                                <div class="section-title">
                                    【风险描述及影响】：
                                </div>
                                <div class="riskDesc-item">
                                    {{ item.riskDesc }}
                                </div>
                            </div>
                            <div
                                v-if="
                                    item.riskSupportList &&
                                    item.riskSupportList.length > 0
                                "
                            >
                                <div class="section-title">
                                    【需支持事项】：
                                </div>
                                <div
                                    v-for="(
                                        support, supportIndex
                                    ) in item.riskSupportList"
                                    :key="`support-${index}-${supportIndex}`"
                                    class="support-item"
                                >
                                    ● {{ support.expectedDate }}，{{
                                        support.supportItem
                                    }}[{{ support.responsibleOrg }}]
                                </div>
                            </div>
                            <div
                                v-else-if="
                                    item.riskSupportList &&
                                    item.riskSupportList.length === 0
                                "
                            >
                                <div class="section-title">
                                    【需支持事项】：
                                </div>
                                <div class="support-item">● 无</div>
                            </div>
                        </div>
                    </div>
                    <div v-else>无风险</div>
                </template>
            </el-table-column>
        </el-table>
        <SupplementDialog
            :visible.sync="supplementDialogVisible"
            @success="getTableData"
            :supplement="supplement"
            :id="taskId"
        ></SupplementDialog>
        <SelectDefectDialog
            @success="getTableData"
            :visible.sync="selectDefectDialogVisible"
        ></SelectDefectDialog>
    </div>
</template>

<script>
import Header from '../Header';
import SupplementDialog from 'maintenanceProject/views/maintenanceReport/components/SupplementDialog';
import SelectDefectDialog from './SelectDefectDialog';
import { getUserAccount } from 'feature/views/meetingManagement/commonFunction';

export default {
    name: 'Defect',
    components: {
        Header,
        SupplementDialog,
        SelectDefectDialog
    },
    props: {},
    data() {
        return {
            tableData: [],
            supplement: '',
            options: {
                weekReportIdGetList: []
            },
            // 每条任务的ID
            taskId: '',
            supplementDialogVisible: false,
            selectDefectDialogVisible: false
        };
    },
    computed: {
        // 维护项目信息：产品线/细分产品线/项目经理
        projectStore() {
            return (
                this.$store.state.maintenanceProject.maintenanceProjectStore ||
                []
            );
        },
        // 周报ID
        weeklyId() {
            return this.$store.state.maintenanceProject.maintenanceReportHeader
                .weeklyId;
        },
        // 当前选中的周报选项（从store获取）
        currentSelectedOption() {
            return this.$store.state.maintenanceProject.currentSelectedOption;
        },
        // 是否是项目经理
        isProjectManager() {
            // 不选到项目经理，无法编辑
            if (this.projectStore.length !== 3) return false;
            return getUserAccount(this) === this.projectStore[2];
        },
        isPqa() {
            return (
                this.$store.state.maintenanceProject.maintenanceReportHeader
                    .isPqa || false
            );
        },
        // 是否能够编辑，待更新才能编辑
        isEditable() {
            return (
                this.$store.state.maintenanceProject.maintenanceReportHeader
                    .weeklyStatus === '待更新'
            );
        }
    },
    methods: {
        /**
         * 处理选择缺陷按钮点击事件
         * 打开选择缺陷对话框
         */
        handleSelectDefect() {
            this.selectDefectDialogVisible = true;
        },
        /**
         * 获取表格数据
         * 使用store中的当前选中周报选项
         */
        async getTableData() {
            if (this.currentSelectedOption.weekReportIdGetList.length === 0) {
                this.tableData = [];
                return;
            }
            // 存一下选择的项目周报选项，给弹窗使用
            this.options = this.currentSelectedOption;
            const api =
                this.$service.maintenanceProject.weekly.getDefectListByWeeklyId;
            const params = this.currentSelectedOption.weekReportIdGetList;
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.tableData = res.body;
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 处理计划项点击事件
         * 打开补充信息对话框
         * @param {Object} item - 被点击的计划项数据
         */
        handlePlanItemClick(item) {
            this.supplementDialogVisible = true;
            this.supplement = item.taskNote;
            this.taskId = item.id;
        },
        /**
         * 判断计划项是否可点击
         * @param {Object} item - 计划项数据
         * @returns {boolean} 是否可点击
         */
        isClickable(item) {
            if (!this.isEditable) return false;
            // 不选择到项目经理（三级），无法编辑
            if (this.projectStore.length !== 3) return false;
            // 如果是任务对应的责任人/项目经理/PQA，就能编辑
            return (
                getUserAccount(this) === item.responsiblePersonAccount ||
                this.isProjectManager ||
                this.isPqa
            );
        }
    }
};
</script>

<style lang="scss" scoped>
.pre-line {
    white-space: pre-line;
}
.deadline-container {
    display: flex;
    flex-direction: column;
    align-items: center;

    div:nth-child(odd) {
        font-weight: bold;
    }
}

.response-plan-container {
    padding: 0 10px;

    .plan-item {
        margin: 5px 0;
        padding-left: 15px;
    }
    .plan-item-content {
        &.clickable {
            cursor: pointer;
            &:hover {
                color: #409eff;
                background: #f5f7fa;
            }
        }
    }
    .taskNote {
        white-space: pre-line;
        margin-top: 5px;
        margin-left: 15px;
        color: #606266;
        font-style: italic;
        background-color: #ffffcc;
    }
}

.riskDesc-support-container {
    padding: 0 10px;
    // 非第一个标题，添加10px的间距
    &:not(:first-child) {
        margin-top: 20px;
    }
    .section-title {
        font-weight: bold;
    }

    .riskDesc-item,
    .support-item {
        margin: 5px 0;
        padding-left: 15px;
    }
}

.select-defect-button {
    margin-right: 15px;
}
.defect-name-container {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}
.product-model-container {
    display: flex;
    flex-direction: column;
    align-items: center;

    .project-manager-container {
        font-weight: bold;
    }
}
</style>
