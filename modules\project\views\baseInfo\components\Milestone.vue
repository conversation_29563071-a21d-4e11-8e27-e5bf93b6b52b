<template>
    <div class="milestone-card-container">
        <el-empty
            description="暂无数据"
            :image-size="30"
            v-if="milestoneData.length === 0"
        ></el-empty>
        <div class="milestone-color-intro" v-else>
            <div class="flex">
                <svg-icon
                    icon-class="project-clock"
                    class="milestone-flag"
                    style="fill: #00a745"
                />
                <span>正常进行</span>
                <svg-icon
                    icon-class="project-clock"
                    class="milestone-flag"
                    style="fill: #e59500"
                />
                <span>有延期风险</span>
                <svg-icon
                    icon-class="project-clock"
                    class="milestone-flag"
                    style="fill: red"
                />
                <span>已延期</span>
                <svg-icon
                    icon-class="project-check-mark"
                    class="check-mark-flag"
                />
                <span>已完成</span>
            </div>
        </div>
        <div
            class="milestone-bar"
            ref="milestoneRef"
            v-show="milestoneData.length !== 0"
        >
            <div class="milestone-info">
                <div
                    style="margin-top: 20px"
                    :style="{
                        visibility: changTimes === 0 ? 'hidden' : 'initial'
                    }"
                >
                    变更次数：{{ changTimes }}次
                </div>
                <div>
                    <div class="milestone-info-title"><b>里程碑</b></div>
                    <div class="milestone-info-plan-time">计划完成时间</div>
                    <div class="milestone-info-actual-time">实际完成时间</div>
                </div>
            </div>
            <div
                v-for="(_, index) in milestoneData"
                class="milestone-item"
                :key="index"
                :style="{
                    width: `${mileStoneWidth}px`
                }"
            >
                <MilestoneBar :data="milestoneData[index]"></MilestoneBar>
            </div>
        </div>
    </div>
</template>
<script>
import MilestoneBar from 'project/views/baseInfo/components/MilestoneBar.vue';
import { debounce } from 'lodash';

export default {
    name: 'Milestone',
    components: { MilestoneBar },
    props: {
        projectId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            mileStoneWidth: 0,
            milestoneData: [],
            changTimes: 0,
            lastWidh: 0
        };
    },
    watch: {
        projectId(newVal) {
            if (newVal) {
                this.lastWidh = 0;
                this.getMilestoneInfo();
            }
        }
    },
    mounted() {
        this.projectId && this.getMilestoneInfo();
    },
    methods: {
        /**
         * 确定每个里程碑块的长度
         * @param {Number} width 里程碑总体长度
         */
        setMilestone(width) {
            if (width < 1000) return;
            if (this.milestoneData.length <= 3) {
                this.mileStoneWidth = 200;
                return;
            }
            this.mileStoneWidth = Math.floor(
                (width - 140) / this.milestoneData.length
            );
        },
        async getMilestoneInfo() {
            try {
                const api = this.$service.project.dashboard.getMileStoneInfo;
                const params = {
                    projectId: this.projectId,
                    projectSourceList: ['IPD']
                };
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.changTimes = res.body.changeTimes || 0;
                this.milestoneData =
                    res.body.projectDetailDashboardDetailVoList;
                const milestoneELement = this.$refs.milestoneRef;
                const observer = new ResizeObserver(
                    debounce((entries) => {
                        for (const entry of entries) {
                            const { width } = entry.contentRect;
                            if (Math.abs(width - this.lastWidh) <= 30) return;
                            this.lastWidh = width;
                            this.setMilestone(width);
                        }
                    }, 100)
                );
                observer.observe(milestoneELement);
            } catch (err) {
                console.error('Error:', err);
            }
        }
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
.milestone-card-container {
    width: 100%;
    overflow: auto;
    height: 160px;
}
.milestone-color-intro {
    display: flex;
    height: 20px;
    margin-left: 10px;
    display: flex;
    align-items: center;
    span {
        line-height: 20px;
    }
    .prefix-circle {
        height: 15px;
        width: 15px;
        border-radius: 50%;
        margin: 1px 5px 0 5px;
    }
    .check-mark-flag {
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background-color: #0064f0;
        border: 2px solid #fff;
        margin: 0 5px;
    }
}
.milestone-flag {
    width: 20px;
    height: 20px;
    margin: 0 5px 0px 5px;
}
.milestone-bar {
    display: flex;
    .milestone-info {
        min-width: 140px;
        height: 120px;
        padding-left: 15px;
        position: relative;
        .milestone-info-title {
            margin-top: 10px;
        }
        .milestone-info-plan-time {
            position: absolute;
            bottom: 14px;
        }
        .milestone-info-actual-time {
            position: absolute;
            bottom: -8px;
        }
    }
    .milestone-item {
        margin-top: 20px;
        min-width: 123px;
    }
}
</style>
