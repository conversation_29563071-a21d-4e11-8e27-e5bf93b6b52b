<template>
    <div>
        <div class="flex" ref="overallChartBox">
            <el-card class="chart-box">
                <div class="flex">
                    <div class="chart-box-title">总体执行情况</div>
                    <el-radio-group
                        class="radio-group"
                        v-model="overallType"
                        @input="handleClick"
                    >
                        <el-radio-button label="waterfall">
                            <svg-icon
                                icon-class="waterfall-chart"
                                class="select-icon"
                            />
                        </el-radio-button>
                        <el-radio-button label="bar">
                            <svg-icon
                                icon-class="barChart"
                                class="select-icon"
                            />
                        </el-radio-button>
                    </el-radio-group>
                </div>
                <el-empty
                    description="暂无数据"
                    :image-size="60"
                    v-if="!projectId"
                    class="empty-status"
                ></el-empty>
                <LiquidChart
                    v-show="overallType === 'waterfall' && projectId"
                    ref="liquidChart"
                    v-loading="loading"
                    v-bind="liquidConfig"
                    class="liquid-chart"
                />
                <div
                    v-show="overallType === 'bar'"
                    ref="overallChart"
                    class="bar-chart"
                ></div>
            </el-card>
            <el-card class="phase-chart">
                <div class="flex">
                    <div class="chart-box-title ml-20">
                        按阶段执行情况<span v-if="currentPhase"
                            >（当前阶段：{{ currentPhase }}）</span
                        >
                    </div>
                    <el-radio-group
                        class="radio-group mr-20"
                        v-model="phaseType"
                        @input="handleClick"
                    >
                        <el-radio-button label="bar">
                            <svg-icon
                                icon-class="barChart"
                                class="select-icon"
                            />
                        </el-radio-button>
                        <el-radio-button label="pie">
                            <svg-icon
                                icon-class="pie-chart"
                                class="select-icon"
                            />
                        </el-radio-button>
                    </el-radio-group>
                </div>
                <el-empty
                    description="暂无数据"
                    :image-size="60"
                    v-show="!projectId"
                    class="empty-status"
                ></el-empty>
                <div
                    ref="phaseChart"
                    class="bar-chart"
                    v-show="phaseType === 'bar' && !!projectId"
                ></div>
                <div class="flex" v-show="phaseType === 'pie' && !!projectId">
                    <div class="pie-chart-wrapper">
                        <div class="chart-box--small--title ml-20">
                            预算比例
                        </div>
                        <div ref="phaseBudgetChart" class="pie-chart"></div>
                    </div>
                    <div class="pie-chart-wrapper">
                        <div class="chart-box--small--title ml-20">
                            实际比例
                        </div>
                        <div ref="phaseActualChart" class="pie-chart"></div>
                    </div>
                </div>
            </el-card>
        </div>
        <el-card class="subject-chart">
            <div class="flex">
                <div class="chart-box-title">按科目执行情况</div>
                <el-radio-group
                    class="radio-group"
                    v-model="subjectType"
                    @input="handleClick"
                >
                    <el-radio-button label="bar">
                        <svg-icon icon-class="barChart" class="select-icon" />
                    </el-radio-button>
                    <el-radio-button label="pie">
                        <svg-icon icon-class="pie-chart" class="select-icon" />
                    </el-radio-button>
                </el-radio-group>
            </div>
            <el-empty
                description="暂无数据"
                :image-size="60"
                v-show="!projectId"
                class="empty-status"
            ></el-empty>
            <div
                ref="subjectChart"
                class="bar-chart"
                v-show="subjectType === 'bar' && !!projectId"
            ></div>
            <div
                class="flex"
                v-show="subjectType === 'pie' && !!projectId"
                style="width: 100%"
            >
                <div class="pie-chart-wrapper">
                    <div class="chart-box--small--title">预算比例</div>
                    <div ref="subjectBudgetChart" class="pie-chart"></div>
                </div>
                <div class="pie-chart-wrapper">
                    <div class="chart-box--small--title">实际比例</div>
                    <div ref="subjectActualChart" class="pie-chart"></div>
                </div>
            </div>
        </el-card>
    </div>
</template>

<script>
import { LiquidChart } from '@opd/g2plot-vue';
import {
    getOverallOptions,
    getPhaseOptions,
    getPhaseBudgetOptions,
    getPhaseActualOptions,
    getSubjectOptions,
    getSubjectBudgetOptions,
    getSubjectActualOptions
} from './chartOptions.js';
import * as echarts from 'echarts';
import { debounce } from 'lodash';

export default {
    components: {
        LiquidChart
    },
    props: {
        projectId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            // 总体执行情况图标切换按钮
            overallType: 'waterfall',
            // 按阶段执行情况图标切换按钮
            phaseType: 'bar',
            // 按科目执行情况图标切换按钮
            subjectType: 'bar',
            loading: false,
            // 水波图配置
            liquidConfig: {
                percent: 0,
                radius: 0.9,
                outline: {
                    border: 2,
                    distance: 4
                },
                statistic: {
                    title: {
                        offsetY: -8,
                        formatter: () => '预算支出比率',
                        style: ({ percent }) => ({
                            fontSize: 14,
                            fill: percent < 0.6 ? '#919191' : 'white'
                        })
                    },
                    content: {
                        style: ({ percent }) => ({
                            fontSize: 30,
                            fill:
                                percent < 0.5
                                    ? 'rgba(44, 53, 66, 0.85)'
                                    : 'white'
                        })
                    }
                },
                liquidStyle: ({ percent }) => ({
                    fill: percent < 1 ? '#5B8FF9' : '#FF0033',
                    stroke: percent < 1 ? '#5B8FF9' : '#FF0033'
                })
            },
            // 图表缓存
            charts: {},
            optionsMap: {},
            // 当前阶段
            currentPhase: '',
            getOverallChartInfo:
                this.$service.project.finance.getOverallChartInfo,
            getPhaseChartInfo: this.$service.project.finance.getPhaseChartInfo,
            getSubjectChartInfo:
                this.$service.project.finance.getSubjectChartInfo
        };
    },
    watch: {
        projectId(newVal) {
            if (newVal) {
                this.getChartInfo();
            }
        }
    },
    mounted() {
        this.projectId && this.getChartInfo();
    },
    methods: {
        /**
         * 初始化图表
         * @param {String} type 图表
         */
        initChart(type) {
            const chartDom = this.$refs[`${type}Chart`];
            if (!chartDom) return;
            // 已经存在了就使用缓存
            if (this.charts[type]) {
                this.charts[type].setOption(this.optionsMap[type]);
                return;
            }
            const myChart = echarts.init(chartDom);
            myChart.setOption(this.optionsMap[type]);
            const { overallChartBox } = this.$refs;
            const observer = new ResizeObserver(
                debounce((entries) => {
                    myChart.resize();
                }, 100)
            );
            observer.observe(overallChartBox);
            // 存储 echarts 实例，以便后续重绘使用
            this.$set(this.charts, type, myChart);
        },
        /**
         * 切换图表显示时，重新确定图表尺寸
         */
        handleClick() {
            this.$nextTick(() => {
                for (const option in this.optionsMap) {
                    if (
                        Object.prototype.hasOwnProperty.call(
                            this.optionsMap,
                            option
                        )
                    ) {
                        this.charts[option] && this.charts[option].resize();
                    }
                }
            });
        },
        async getChartInfo() {
            const param = { projectId: this.projectId };
            const resList = await Promise.all([
                this.getOverallChartInfo(param),
                this.getPhaseChartInfo(param),
                this.getSubjectChartInfo(param)
            ]);
            for (const res of resList) {
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
            }
            this.liquidConfig.percent =
                parseFloat(resList[0].body.budgetCostRatio) / 100;
            this.optionsMap.overall = getOverallOptions(resList[0].body);
            this.currentPhase = resList[1].body.currProjectStage;
            // 处理迁移阶段的数据，如果预算和实际支出都为0.0（代表OA接口未提供），则不显示
            const { budgetAmount, costAmount } =
                resList[1].body.projectFinanceByGroupVoList[4];
            if (budgetAmount === '0.00' && costAmount === '0.00') {
                resList[1].body.projectFinanceByGroupVoList.pop();
            }

            this.optionsMap.phase = getPhaseOptions(resList[1].body);
            this.optionsMap.phaseBudget = getPhaseBudgetOptions(
                resList[1].body
            );
            this.optionsMap.phaseActual = getPhaseActualOptions(
                resList[1].body
            );
            this.optionsMap.subject = getSubjectOptions(resList[2].body);
            this.optionsMap.subjectBudget = getSubjectBudgetOptions(
                resList[2].body
            );
            this.optionsMap.subjectActual = getSubjectActualOptions(
                resList[2].body
            );
            for (const option in this.optionsMap) {
                if (
                    Object.prototype.hasOwnProperty.call(
                        this.optionsMap,
                        option
                    )
                ) {
                    this.initChart(option);
                }
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.mr-20 {
    margin-right: 20px;
}
.ml-20 {
    margin-left: 20px;
}
.chart-box {
    width: 400px;
    height: 400px;
    padding-bottom: 0;
    .liquid-chart {
        height: 320px;
        width: 260px;
        display: flex;
        margin: auto;
    }
    .bar-chart {
        height: 320px;
        width: 350px;
    }
}
.overall-cost--info {
    display: flex;
    flex-direction: column;
    margin: auto;
    font-size: 16px;
    height: 100%;
    .overall-cost--info--title {
        margin-bottom: 15px;
        span {
            font-weight: 600;
            margin: 0 8px;
            color: #0054bb;
            font-size: 24px;
        }
    }
}
.chart-box-title {
    background-color: #3370ff;
    color: #fff;
    font-weight: 600;
    text-align: center;
    font-size: 12px;
    width: fit-content;
    padding: 8px;
    border-radius: 10px;
}
.chart-box--small--title {
    margin-top: 5px;
    background-color: #3370ff;
    color: #fff;
    font-weight: 600;
    text-align: center;
    font-size: 12px;
    width: fit-content;
    padding: 5px;
    border-radius: 8px;
}

.select-icon {
    height: 15px;
    width: 15px;
}
.radio-group {
    margin-left: auto;
}
.subject-chart {
    width: 100%;
    height: 400px;
    margin-top: 16px;
    margin-bottom: 20px;
    padding-bottom: 0;
    .bar-chart {
        height: 320px;
        width: 100%;
    }
    .pie-chart {
        height: 90%;
        width: 100%;
    }
    .pie-chart-wrapper {
        height: 320px;
        width: 50%;
    }
}
.phase-chart {
    margin-left: 16px;
    flex: 1;
    height: 400px;
    padding: 20px 0;
    .bar-chart {
        height: 320px;
        width: 100%;
    }
    .pie-chart {
        height: 90%;
        width: 100%;
    }
    .pie-chart-wrapper {
        height: 320px;
        width: 50%;
    }
}
.empty-status {
    margin-top: 30px;
}
</style>
