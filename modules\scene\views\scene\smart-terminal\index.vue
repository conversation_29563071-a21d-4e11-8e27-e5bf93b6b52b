<template>
    <div style="overflow: scroll; height: 100vh; position: relative">
        <nav-tabs-vue
            class="tab-view"
            :which-index="whichIndex"
            :cardArr="cardArr"
        ></nav-tabs-vue>
        <AddProjectByProduct
            businessUnit="智能自助终端"
            @update="update"
            :data="list"
        ></AddProjectByProduct>
        <DeleteProject
            businessUnit="智能自助终端"
            :productData="list"
            @update="update"
        ></DeleteProject>
        <div
            class="product-overview-container"
            :style="{
                height: `${containerHeight}px`
            }"
        >
            <div
                class="product-overview"
                :style="{ transform: `scale(${scale})` }"
                ref="productOverviewRef"
            >
                <div class="product-overview-title">
                    智能自助终端总计
                    <span class="bold-number">{{
                        description.totalProductTypeNum
                    }}</span>
                    类产品，从项目维度目前总计开发
                    <span class="bold-number">{{
                        description.totalProjectNum
                    }}</span
                    >个项目，其中已发布
                    <span class="bold-number">
                        {{ description.publishProjectNum }}</span
                    >个项目，在研
                    <span class="bold-number">
                        {{ description.doingProjectNum }}</span
                    >个项目，规划中
                    <span class="bold-number">{{
                        description.planProjectNum
                    }}</span>
                    个项目。
                </div>
                <!-- 产品情况表 -->
                <product-situation ref="productSituationRef" />
                <!-- 产品分类图 -->
                <product-category
                    ref="productCategoryRef"
                    :config="productCategroyList"
                />
                <!-- 产品列表 -->
                <product-list
                    ref="productListRef"
                    :config="productCategroyList"
                    :list="list"
                />
            </div>
        </div>
        <el-button type="primary" @click="goback" class="goback-button"
            >返回</el-button
        >
    </div>
</template>

<script>
import ProductSituation from 'scene/views/scene/smart-terminal/components/ProductSituation.vue';
import ProductCategory from 'scene/views/scene/smart-terminal/components/ProductCategory.vue';
import ProductList from 'scene/views/scene/components/ProductWholeList.vue';
import NavTabsVue from '../components/NavTabs.vue';
import AddProjectByProduct from 'scene/views/scene/components/AddProjectByProduct.vue';
import DeleteProject from 'scene/views/scene/components/DeleteProject.vue';

const { origin, hostname } = window.location;
let ori = origin;
if (hostname === '127.0.0.1' || hostname === 'localhost') {
    ori = 'http://dev-pmis.xtjc.net';
}
export default {
    name: 'SmartTerminal',
    components: {
        ProductSituation,
        ProductCategory,
        ProductList,
        NavTabsVue,
        AddProjectByProduct,
        DeleteProject
    },
    data() {
        return {
            containerHeight: 1820,
            scale: 1,
            // 图表实例
            sceneChart: null,
            list: [],
            productCategroyList: [],
            description: {},
            // 上一次的宽度
            lastWidth: 0,
            whichIndex: 0,
            cardArr: [
                {
                    componentName: '产品综述'
                }
            ]
        };
    },
    computed: {},
    beforeDestroy() {
        const container = this.$refs.productOverviewRef;
        const parent = container.parentElement;
        if (this.resizeObserver) {
            this.resizeObserver.unobserve(parent);
        }
    },
    mounted() {
        // 计算产品综述整体节点的缩放及高度值
        this.computeContainerStyle();
        // 监听父元素尺寸变化，重新计算缩放比例
        this.initResizeObserver();
        // 调用接口获取产品情况列表
        this.getFinanceProductOverviewCoreList();
        // 缓存页面
        this.$store.dispatch('tagsView/addView', this.$route);
        // 调用接口获取配置信息
        this.getProductSetList();
        // 获取产品综述信息
        this.getDescription();
    },
    methods: {
        computeContainerStyle() {
            const container = this.$refs.productOverviewRef;
            const parent = container.parentElement;
            this.scale = parent.offsetWidth / container.offsetWidth;
            this.containerHeight = container.offsetHeight * this.scale;
        },
        // 监听父元素尺寸变化，重新计算缩放比例
        initResizeObserver() {
            const el = this.$refs.productOverviewRef.parentElement;
            this.lastWidth = el.offsetWidth;
            // 定义变化的阈值
            const THRESHOLD = 50;
            if (el) {
                this.resizeObserver = new ResizeObserver(() => {
                    const currentWidth = el.offsetWidth;
                    if (Math.abs(currentWidth - this.lastWidth) >= THRESHOLD) {
                        this.computeContainerStyle();
                        // 记录当前宽度
                        this.lastWidth = currentWidth;
                    }
                });
                this.resizeObserver.observe(el);
            }
        },
        // 核心零部件情况查询并绘制echars图
        async getFinanceProductOverviewCoreList() {
            this.$refs.productSituationRef.coreChart.showLoading();
            try {
                const params = {
                    businessUnit: '智能自助终端'
                };
                const res =
                    await this.$service.scene.finance.getProductOverviewWholeList(
                        params
                    );
                if (res.head.code === '000000') {
                    const list = res.body || [];
                    this.$refs.productSituationRef.drawChart(list, 'coreChart');
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
                this.$message.error('系统异常');
            } finally {
                this.$refs.productSituationRef.coreChart.hideLoading();
            }
        },
        goback() {
            this.$router.push('/dashboard-index');
        },
        // 产品分类查询
        async getProductSetList() {
            try {
                const params = {
                    businessUnit: '智能自助终端'
                };
                const res =
                    await this.$service.scene.finance.getProductSetListInWholeScene(
                        params
                    );
                if (res.head.code === '000000') {
                    const list = res.body || [];
                    const width = `${Math.floor(100 / list.length)}%`;
                    this.productCategroyList = list.map((i) => {
                        return {
                            name: i.productSet,
                            style: { width },
                            img: `${ori}/upload/${i.iconName}.png`
                        };
                    });
                    this.list = list;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
                this.$message.error('系统异常');
            }
        },
        // 产品综述查询
        async getDescription() {
            try {
                const params = {
                    businessUnit: '智能自助终端'
                };
                const res =
                    await this.$service.scene.business_project_finance.getOverviewOtherDescription(
                        params
                    );
                if (res.head.code === '000000') {
                    this.description = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
                this.$message.error('系统异常');
            }
        },
        update() {
            // 调用接口获取产品情况列表
            this.getFinanceProductOverviewCoreList();
            // 调用接口获取配置信息
            this.getProductSetList();
            // 获取产品综述信息
            this.getDescription();
        }
    }
};
</script>

<style lang="scss" scoped>
.product-overview-container {
    width: 100%;
    background: url(~scene/assets/product-overview-page-bg.png) no-repeat;
    background-size: cover;
    overflow: hidden;
    .product-overview {
        width: 2503px;
        height: 1896px;
        transform-origin: 0 0;
        overflow: auto;
        padding: 116px 100px 0 100px;
        .product-overview-title {
            display: flex;
            align-items: center;
            justify-content: flex-start;
            width: 100%;
            height: 120px;
            border: 1px solid #ffffff;
            border-radius: 20px;
            color: #00123d;
            background: linear-gradient(180deg, #d4e7fe 0%, #e1e8fa 100%);
            text-align: center;
            font-size: 28px;
            margin: 0 auto 20px auto;
            font-weight: 500;
            line-height: auto;
            padding-left: 20px;
            > div {
                margin: 10px;
            }
            .bold-number {
                font-weight: 600;
                margin: 0 8px;
                color: #0054bb;
                font-size: 34px;
            }
        }
    }
}
.goback-button {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 100;
}
.tab-view {
    position: absolute;
    top: 10px;
    z-index: 1;
    width: 15%;
    left: 42.5%;
}
</style>
