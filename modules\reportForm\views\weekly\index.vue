<template>
    <div class="product-line-container">
        <!-- 产品线周报标题 -->
        <div class="product-line-title">产品线周报</div>

        <!-- 产品卡片网格 -->
        <div class="product-cards">
            <div
                v-for="product in productLines"
                :key="product.id"
                class="product-card"
            >
                <div class="product-card-content">
                    <div class="product-card-title">{{ product.name }}</div>
                    <div class="product-card-actions">
                        <el-button
                            v-for="action in product.actions"
                            :key="action.type"
                            type="primary"
                            class="product-card-action"
                            @click="handleProductAction(product, action)"
                        >
                            {{ action.name }}
                        </el-button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 支持事项部分 -->
        <div class="support-section">
            <div class="support-header">
                <div class="support-title">
                    本周需部门支持事项（新增
                    <span class="new-count">{{ supportSummary.newCount }}</span>
                    项 / 共
                    <span style="font-weight: bold">{{
                        supportSummary.totalCount
                    }}</span>
                    项）
                </div>
                <div class="support-filters">
                    <div
                        v-for="filter in supportFilters"
                        :key="filter.value"
                        class="filter-item"
                        @click="handleFilterChange(filter.value)"
                    >
                        <div
                            class="filter-radio"
                            :class="{ active: currentFilter === filter.value }"
                        ></div>
                        <span>{{ filter.label }}</span>
                    </div>
                </div>
            </div>

            <!-- 支持卡片网格 -->
            <div
                v-for="(row, rowIndex) in supportCardRows"
                :key="rowIndex"
                class="support-cards"
            >
                <div
                    v-for="card in row"
                    :key="card.id"
                    class="support-card"
                    @click="handleSupportCardClick(card)"
                >
                    <div class="support-card-title">{{ card.title }}</div>
                    <div class="support-card-stats">
                        新增
                        <span class="new-count">{{ card.newCount }}</span> 项 /
                        共 {{ card.totalCount }} 项
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: 'ReportFormWeekly',
    data() {
        return {
            // 产品线数据
            productLines: [
                {
                    id: 1,
                    name: '打印引擎',
                    actions: [
                        { type: 'dev', name: '开发项目' },
                        { type: 'maintain', name: '维护工作' }
                    ]
                },
                {
                    id: 2,
                    name: '金融设备',
                    actions: [
                        { type: 'dev', name: '开发项目' },
                        { type: 'maintain', name: '维护工作' }
                    ]
                },
                {
                    id: 3,
                    name: '自助终端',
                    actions: [
                        { type: 'dev', name: '开发项目' },
                        { type: 'maintain', name: '维护工作' }
                    ]
                },
                {
                    id: 4,
                    name: '自动化',
                    actions: [
                        { type: 'dev', name: '开发项目' },
                        { type: 'maintain', name: '维护工作' }
                    ]
                },
                {
                    id: 5,
                    name: '关键基础零部件',
                    actions: [
                        { type: 'dev', name: '开发项目' },
                        { type: 'maintain', name: '维护工作' }
                    ]
                },
                {
                    id: 6,
                    name: '对外合作',
                    actions: [
                        { type: 'dev', name: '开发项目' },
                        { type: 'maintain', name: '维护工作' }
                    ]
                },
                {
                    id: 7,
                    name: '技术研发',
                    actions: [
                        { type: 'dev', name: '开发项目' },
                        { type: 'maintain', name: '维护工作' }
                    ]
                },
                {
                    id: 8,
                    name: '系统集成',
                    actions: [
                        { type: 'dev', name: '开发项目' },
                        { type: 'maintain', name: '维护工作' }
                    ]
                }
            ],
            // 支持事项汇总
            supportSummary: {
                newCount: 13,
                totalCount: 16
            },
            // 筛选器选项
            supportFilters: [
                { value: 'department', label: '部门进度' },
                { value: 'productLine', label: '产品线进度' }
            ],
            // 当前选中的筛选器
            currentFilter: 'department',
            // 支持卡片数据
            supportCards: [
                { id: 1, title: '国内销售中心', newCount: 13, totalCount: 16 },
                { id: 2, title: '海外销售中心', newCount: 4, totalCount: 16 },
                { id: 3, title: '市场技术部', newCount: 4, totalCount: 16 },
                { id: 4, title: '计划运营中心', newCount: 4, totalCount: 16 },
                { id: 5, title: '交付中心', newCount: 4, totalCount: 16 },
                { id: 6, title: '质量中心', newCount: 13, totalCount: 16 },
                { id: 7, title: '国内销售中心', newCount: 13, totalCount: 16 },
                { id: 8, title: '海外销售中心', newCount: 4, totalCount: 16 },
                { id: 9, title: '市场技术部', newCount: 4, totalCount: 16 },
                { id: 10, title: '计划运营中心', newCount: 4, totalCount: 16 },
                { id: 11, title: '交付中心', newCount: 4, totalCount: 16 },
                { id: 12, title: '质量中心', newCount: 13, totalCount: 16 },
                { id: 13, title: '国内销售中心', newCount: 13, totalCount: 16 },
                { id: 14, title: '海外销售中心', newCount: 4, totalCount: 16 },
                { id: 15, title: '市场技术部', newCount: 4, totalCount: 16 },
                { id: 16, title: '计划运营中心', newCount: 4, totalCount: 16 },
                { id: 17, title: '交付中心', newCount: 4, totalCount: 16 },
                { id: 18, title: '质量中心', newCount: 13, totalCount: 16 },
                { id: 19, title: '国内销售中心', newCount: 13, totalCount: 16 },
                { id: 20, title: '海外销售中心', newCount: 4, totalCount: 16 },
                { id: 21, title: '市场技术部', newCount: 4, totalCount: 16 },
                { id: 22, title: '计划运营中心', newCount: 4, totalCount: 16 },
                { id: 23, title: '交付中心', newCount: 4, totalCount: 16 },
                { id: 24, title: '质量中心', newCount: 13, totalCount: 16 }
            ]
        };
    },
    computed: {
        // 将支持卡片按行分组（每行6个）
        supportCardRows() {
            const rows = [];
            for (let i = 0; i < this.supportCards.length; i += 6) {
                rows.push(this.supportCards.slice(i, i + 6));
            }
            return rows;
        }
    },
    methods: {
        // 处理产品线操作按钮点击
        handleProductAction(product, action) {
            console.log('产品线操作:', product.name, action.name);
            // 这里可以添加路由跳转或其他逻辑
            this.$message.info(`点击了${product.name}的${action.name}`);
        },
        // 处理筛选器切换
        handleFilterChange(filterValue) {
            this.currentFilter = filterValue;
            console.log('切换筛选器:', filterValue);
            // 这里可以添加数据重新加载逻辑
        },
        // 处理支持卡片点击
        handleSupportCardClick(card) {
            console.log('点击支持卡片:', card.title);
            // 这里可以添加详情页面跳转逻辑
            this.$message.info(`点击了${card.title}`);
        }
    }
};
</script>
<style scoped lang="scss">
.product-line-container {
    padding: 20px;
    background-color: #f5f5f5;
    height: 100vh;
    overflow-y: auto;
}

.product-line-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 20px;
}

.product-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
    margin-bottom: 30px;
}

.product-card {
    background: linear-gradient(135deg, #4169e1 0%, #0066ff 100%);
    color: white;
    padding: 20px 20px 10px 20px;
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s ease;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: repeating-linear-gradient(
            135deg,
            transparent,
            transparent 2px,
            rgba(255, 255, 255, 0.1) 2px,
            rgba(255, 255, 255, 0.1) 4px
        );
    }
}

.product-card-content {
    position: relative;
    z-index: 1;
}

.product-card-title {
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 15px;
}

.product-card-actions {
    display: flex;
    justify-content: space-between;
    gap: 10px;
}

.product-card-action {
    flex: 1;
    padding: 8px 16px;
    background: #3d33ff;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    border: none;
    &:hover {
        background: rgba(61, 51, 255, 0.6);
    }
}

.support-section {
    margin-bottom: 30px;
}

.support-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.support-title {
    font-size: 16px;
    color: #333;
}

.support-filters {
    display: flex;
    gap: 20px;
}

.filter-item {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    transition: opacity 0.2s ease;

    &:hover {
        opacity: 0.8;
    }
}

.filter-radio {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    border: 2px solid #999;
    position: relative;
    transition: border-color 0.2s ease;

    &.active {
        border-color: #409eff;

        &::after {
            content: '';
            width: 6px;
            height: 6px;
            background: #409eff;
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }
}

.support-cards {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 16px;
    margin-bottom: 20px;
}

.support-card {
    background: white;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
}

.support-card-title {
    color: #333;
    margin-bottom: 15px;
    font-weight: 600;
}

.support-card-stats {
    color: #666;
}

.new-count {
    color: #f56c6c;
    font-weight: bold;
}
</style>
