/**
 * 模块store都写在这里,框架会自动注册为模块store
 *
 * 使用方式：this.$store.project.xxx
 */
import { saveStateToStorage } from '../utils/storeHelpers';

// 尝试从 localStorage 获取初始状态
const getInitialState = () => {
    try {
        const storageKeyPrefix = 'si-pmis-project-';
        const projectStoreKey = `${storageKeyPrefix}projectStore`;
        const projectInfoKey = `${storageKeyPrefix}projectInfo`;
        const productLineKey = `${storageKeyPrefix}productLine`;

        // 从 localStorage 读取数据
        const projectStore = localStorage.getItem(projectStoreKey);
        const projectInfo = localStorage.getItem(projectInfoKey);
        const productLine = localStorage.getItem(productLineKey);

        return {
            // 如果 localStorage 中有数据，就使用它，否则使用默认值
            projectStore: projectStore ? JSON.parse(projectStore) : [],
            projectInfo: projectInfo ? JSON.parse(projectInfo) : {},
            productLine: productLine ? JSON.parse(productLine) : [],
            // 其他字段使用默认值
            allEmployeeList: [],
            currentEmployeeList: [],
            employeeQueried: false
        };
    } catch (error) {
        console.error('从 localStorage 获取初始状态失败:', error);
        // 使用默认状态
        return {
            // 顶部级联组件的值
            projectStore: [],
            // 所有人员名单（包含离职人员）
            allEmployeeList: [],
            // 在职人员名单
            currentEmployeeList: [],
            // 是否已经查询过人员名单
            employeeQueried: false,
            // 项目信息
            projectInfo: {},
            // 产品线
            productLine: []
        };
    }
};

const state = getInitialState();

const mutations = {
    CHANGE_PROJECT(state, projectStore) {
        state.projectStore = projectStore;
        // 保存状态到 localStorage
        saveStateToStorage(state);
    },
    CHANGE_ALL_EMPLOYEE_LIST(state, allEmployeeList) {
        state.allEmployeeList = allEmployeeList;
    },
    CHANGE_CURRENT_EMPLOYEE_LIST(state, currentEmployeeList) {
        state.currentEmployeeList = currentEmployeeList;
    },
    CHANGE_EMPLOYEE_QUERIED(state, employeeQueried) {
        state.employeeQueried = employeeQueried;
    },
    CHANGE_PROJECT_INFO(state, projectInfo) {
        state.projectInfo = projectInfo;
        // 保存状态到 localStorage
        saveStateToStorage(state);
    },
    CHANGE_PRODUCT_LINE(state, productLine) {
        state.productLine = productLine;
        // 保存状态到 localStorage
        saveStateToStorage(state);
    }
};

const actions = {
    changeProject({ commit }, projectStore) {
        commit('CHANGE_PROJECT', projectStore);
    },
    changeAllEmployeeList({ commit }, allEmployeeList) {
        commit('CHANGE_ALL_EMPLOYEE_LIST', allEmployeeList);
    },
    changeCurrentEmployeeList({ commit }, currentEmployeeList) {
        commit('CHANGE_CURRENT_EMPLOYEE_LIST', currentEmployeeList);
    },
    changeEmployeeQueried({ commit }, employeeQueried) {
        commit('CHANGE_EMPLOYEE_QUERIED', employeeQueried);
    },
    changeProjectInfo({ commit }, projectInfo) {
        commit('CHANGE_PROJECT_INFO', projectInfo);
    },
    changeProductLine({ commit }, productLine) {
        commit('CHANGE_PRODUCT_LINE', productLine);
    }
};

export default {
    namespaced: true,
    state,
    mutations,
    actions
};
