<template>
    <div class="zandaoModal">
        <el-dialog
            title="人工周费用核算"
            width="80%"
            :visible.sync="dialogVisible"
        >
            <div class="flex info">
                <div><b>项目名称：</b>{{ data.projectName }}</div>
                <div><b>核算周：</b>{{ data.week }}</div>
                <div>
                    <b>核算区间：</b>{{ data.startDate }} ~ {{ data.endDate }}
                </div>
            </div>
            <div style="display: flex; justify-content: end">单位：小时</div>
            <el-table
                :data="tableList"
                :header-cell-style="{
                    'text-align': 'center'
                }"
                max-height="400"
            >
                <el-table-column align="center" label="人员" width="150">
                    <template slot-scope="{ row }">
                        {{
                            `${row.accountName}` +
                            (row.workSource === '软件'
                                ? `（${row.workSource}）`
                                : '')
                        }}
                    </template>
                </el-table-column>
                <el-table-column prop="titleLevel" label="级别" align="center">
                </el-table-column>
                <el-table-column prop="sumHours" label="合计" align="center">
                </el-table-column>
                <el-table-column
                    v-for="(i, index) in week"
                    :label="i"
                    :key="i"
                    align="center"
                >
                    <el-table-column :label="dateArr[index]" align="center">
                        <template slot-scope="scope">
                            <el-popover
                                popper-class="resources-workTimeBar--popper"
                                placement="bottom-start"
                                width="200"
                                trigger="hover"
                                ref="popover"
                                :disabled="scope.row[dateArr[index]] === 0"
                                :popper-options="{
                                    boundariesElement: 'viewport'
                                }"
                            >
                                <div v-if="taskDetail.length > 0">
                                    <div
                                        v-for="(item, index) in taskDetail"
                                        :key="index"
                                    >
                                        <WorkTimeDetailList
                                            :index="index"
                                            :item="item"
                                            :taskDetail="taskDetail"
                                        ></WorkTimeDetailList>
                                    </div>
                                </div>
                                <div
                                    class="hour-click"
                                    :style="{
                                        cursor:
                                            scope.row[dateArr[index]] === 0
                                                ? 'initial'
                                                : 'pointer'
                                    }"
                                    slot="reference"
                                    @mousemove="
                                        debouncedGetHourListInfo(
                                            scope.row,
                                            dateArr[index]
                                        )
                                    "
                                >
                                    {{ scope.row[dateArr[index]] }}
                                </div>
                            </el-popover>
                        </template>
                    </el-table-column>
                </el-table-column>
            </el-table>
            <div class="total-cost">
                当周人工费用合计：<span class="total-cost-numbers"
                    >￥{{
                        data.caculateAmount &&
                        data.caculateAmount.toLocaleString()
                    }}</span
                >
            </div>
            <div class="confirm-button">
                <el-button
                    v-if="
                        data.calculateStatus === '核算中' &&
                        hasEmployeeCostCheckPermission
                    "
                    type="primary"
                    @click="confirm"
                    >确认支出</el-button
                >
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { debounce } from 'lodash';
import WorkTimeDetailList from 'Components/WorkTimeDetailList.vue';

const week = [
    '星期一',
    '星期二',
    '星期三',
    '星期四',
    '星期五',
    '星期六',
    '星期日'
];
export default {
    name: 'EmpolyeeCostDetail',
    components: {
        WorkTimeDetailList
    },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        data: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {
            tableList: [],
            week,
            dateArr: [],
            taskDetail: [],
            save: {}
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        hasEmployeeCostCheckPermission() {
            return this.$store.state.permission.btnDatas.includes(
                'employeeCostCheck'
            );
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.setTableList();
            }
        }
    },
    methods: {
        /**
         * 处理表头数据
         */
        setTableList() {
            this.tableList = [];
            this.dateArr = [];
            this.tableList = this.data.projectWeekHrCostPersonVoList.map(
                (i, index) => {
                    // 打散各个日期，整合到column中
                    i.projectWeekHrCostPersonDetailVoList.forEach((j) => {
                        i[j.date] = j.hours;
                        // 用第一项的值，构成日期数组
                        if (index === 0) {
                            this.dateArr.push(j.date);
                        }
                    });
                    return i;
                }
            );
        },
        confirm() {
            this.$emit('employee-cost-confirm');
        },
        /**
         * 获取每个人的工时列表信息
         * @param {Object} row 每行数据
         * @param {String} date 日期
         */
        async getHourListInfo(row, date) {
            try {
                if (row[date] === 0) {
                    return;
                }
                const api = this.$service.project.finance.getEmoloyeeTaskList;
                const params = {
                    isQueryPlanHour: 'false',
                    loginNames: [row.account],
                    projectId: this.data.projectId,
                    selectDate: date,
                    workSource: row.workSource || '硬件'
                };
                if (this.save[JSON.stringify(params)]) {
                    this.taskDetail = this.save[JSON.stringify(params)];
                    return;
                }
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.taskDetail = res.body;
                this.save[JSON.stringify(params)] = res.body;
                // 数据更新之后令popover重新计算位置
                this.$nextTick(() => {
                    this.$refs.popover.forEach((popover) => {
                        popover.updatePopper && popover.updatePopper();
                    });
                });
            } catch (err) {
                console.error('Error:', err);
            }
        },
        debouncedGetHourListInfo: debounce(function (row, date) {
            this.getHourListInfo(row, date);
        }, 300)
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
.info {
    justify-content: space-between;
    margin-bottom: 20px;
}
.total-cost {
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
    font-size: 14px;
    font-weight: 600;
    margin-top: 20px;
    .total-cost-numbers {
        font-size: 20px;
        text-decoration: underline;
    }
}
.confirm-button {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;
}
// 统一表头高度，修正操作列错位
::v-deep .el-table__header {
    padding: 0;
    height: 50px !important;
}
// 留给人员展示更大空间
::v-deep .table-person .cell {
    padding-left: 0 !important;
}
::v-deep .el-table--mini .el-table__cell {
    padding: 3px 0 !important;
}
::v-deep .workHour .cell {
    padding: 0 auto !important;
    display: flex;
    justify-content: center;
}
::v-deep .el-table .el-table__row {
    height: auto !important;
}
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border-right: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border-bottom: 1px solid #dfe6ec !important;
}
</style>
<style lang="scss">
// 弹窗样式，与app同级，只能写在这里
.el-popper.el-popover.resources-workTimeBar--popper {
    max-height: 360px;
    overflow: auto;
    width: 420px !important;
}
</style>
