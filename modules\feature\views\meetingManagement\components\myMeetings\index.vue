<!-- 我的会议 -->
<template>
    <div class="meetings-container">
        <el-button type="primary" class="fixed-button" @click="createMeeting"
            >创建会议</el-button
        >
        <div v-if="meetingManagementList.length != 0">
            <el-divider class="divider">会议管理</el-divider>
            <MyMeetingsManagement
                ref="meetingsManagement"
                class="management-list"
                :tableList="meetingManagementList"
                @update="updateData"
            ></MyMeetingsManagement>
        </div>
        <el-divider class="divider">周会议列表</el-divider>
        <div class="flex">
            <el-date-picker
                v-model="week"
                type="week"
                placeholder="选择周"
                ref="dataPicker"
                :format="dateFormat"
                :clearable="false"
                :picker-options="pickerOptions"
            >
            </el-date-picker>
            <el-button
                type="primary"
                @click="handleQuickAccess('本周')"
                class="quick-access"
                >本周</el-button
            >
            <el-button type="primary" @click="handleQuickAccess('下周')"
                >下周</el-button
            >
        </div>
        <MyMeetingsQueryList
            class="query-list"
            :tableList="meetingList"
            :daysOfWeek="daysOfWeek"
        ></MyMeetingsQueryList>

        <MeetingUpdate
            :visible.sync="meetingUpdateVisible"
            title="创建会议"
            @update="updateData"
        ></MeetingUpdate>
    </div>
</template>

<script>
import moment from 'moment';
import MyMeetingsQueryList from './MyMeetingsQueryList.vue';
import MyMeetingsManagement from './MyMeetingsManagement.vue';
import MeetingUpdate from '../modals/meetingUpdate';
import { getDaysOfWeek } from '../../commonFunction';
import { getExternalStaffPeople } from 'feature/views/meetingManagement/commonFunction';
// 设置一周的开始为周一，不设置的话，默认是周末
moment.updateLocale('zh-cn', {
    week: {
        dow: 1
    }
});

export default {
    name: 'MyMeetings',
    components: { MyMeetingsQueryList, MyMeetingsManagement, MeetingUpdate },
    props: {
        activeName: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            week: new Date(),
            pickerOptions: {
                firstDayOfWeek: 1
            },
            meetingManagementList: [],
            meetingList: [],
            meetingUpdateVisible: false
        };
    },
    computed: {
        // 展示的格式
        dateFormat() {
            const startOfWeek = moment(this.week)
                .startOf('week')
                .format('YYYY/M/D');
            const endOfWeek = moment(this.week)
                .endOf('week')
                .format('YYYY/M/D');
            return `${startOfWeek} - ${endOfWeek}`;
        },
        // 一周内的时间，例如'1月1日'
        daysOfWeek() {
            return getDaysOfWeek(this.week);
        }
    },
    watch: {
        week(newVal) {
            newVal && this.getMeetingList();
        },
        activeName(newVal) {
            newVal === 'myMeetings' && this.updateData();
        }
    },
    created() {
        this.getMeetingManagementList();
        this.getMeetingList();
        getExternalStaffPeople(this);
    },
    methods: {
        /**
         * 点击快捷按钮的处理
         * @param {String} type 按钮类型
         */
        handleQuickAccess(type) {
            const currentDate = new Date();
            if (type === '本周') {
                this.week = currentDate;
            } else if (type === '下周') {
                const nextWeekDate = new Date();
                nextWeekDate.setDate(currentDate.getDate() + 7);
                this.week = nextWeekDate;
            }
        },
        /**
         * 获取会议管理任务列表
         */
        async getMeetingManagementList() {
            const api = this.$service.feature.myMeetings.taskList;
            try {
                const res = await api();
                if (res.head.code === '000000') {
                    this.meetingManagementList = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 获取周会议列表数据
         */
        async getMeetingList() {
            const api = this.$service.feature.myMeetings.list;
            try {
                const startOfWeek = moment(this.week)
                    .startOf('week')
                    .format('YYYY-MM-DD');
                const endOfWeek = moment(this.week)
                    .endOf('week')
                    .format('YYYY-MM-DD');

                const res = await api({
                    startDate: startOfWeek,
                    endDate: endOfWeek
                });

                if (res.head.code === '000000') {
                    this.handleMeetingList(res.body);
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 创建会议
         */
        createMeeting() {
            this.meetingUpdateVisible = true;
        },
        /**
         * 处理会议列表数据结构
         * @param {Array} data 原始会议列表数据
         */
        handleMeetingList(data) {
            // 将每个日期作为key
            const dateObj = [...getDaysOfWeek(this.week), '其他'].reduce(
                (acc, date) => {
                    acc[date] = '';
                    return acc;
                },
                {}
            );
            this.meetingList = data.map((item) => {
                const res = { ...item, ...dateObj };
                res[item.dateVal] = item.meetingStartTime;
                return res;
            });
        },
        /**
         * 弹窗关闭后更新数据
         */
        updateData() {
            this.getMeetingManagementList();
            this.getMeetingList();
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}

.meetings-container {
    height: calc(100vh - 100px);
    overflow: auto;
    position: relative;
    .divider {
        .el-divider__text {
            font-weight: 800;
        }
    }
    .fixed-button {
        position: absolute;
        top: 10px;
        right: 5px;
        z-index: 1;
    }
    .meeting-type {
        margin-left: auto;
    }
    .quick-access {
        margin-left: 10px;
    }
    .query-list {
        margin-top: 10px;
    }
    .management-list {
        margin-top: 15px;
        margin-bottom: 25px;
    }
}
</style>
