<template>
    <div class="finance-container">
        <project-selector
            @input="handleChange"
            :key="projectSelectorKey"
            :queryOnMount="isQueryOnMount"
        ></project-selector>
        <el-tabs v-model="activeName" class="tabs">
            <el-tab-pane label="当前基线" name="currentBaseLine" :lazy="true"
                ><CurrentBaseLine :projectId="projectId"></CurrentBaseLine>
            </el-tab-pane>
            <el-tab-pane label="支出明细" name="expenseDetails" :lazy="true">
                <ExpenseDetails :projectId="projectId"></ExpenseDetails>
            </el-tab-pane>
        </el-tabs>
        <el-button
            type="primary"
            @click="goback"
            class="goback-button"
            v-show="this.$route.query.id"
            >返回</el-button
        >
    </div>
</template>

<script>
import ProjectSelector from 'project/components/projectSelector.vue';
import CurrentBaseLine from './components/CurrentBaseLine.vue';
import ExpenseDetails from './components/ExpenseDetails.vue';

export default {
    name: 'BaseInfo',
    components: { ProjectSelector, CurrentBaseLine, ExpenseDetails },
    data() {
        return {
            projectSelectorKey: 0,
            activeName: 'currentBaseLine',
            projectId: '',
            // 挂载时是否查询
            isQueryOnMount: false
        };
    },
    created() {
        // 传入id进行查询
        if (this.$route.query.id) {
            this.projectId = this.$route.query.id;
            const value = [this.$route.query.productLine, this.projectId];
            this.activeName = 'expenseDetails';
            this.$store.dispatch('project/changeProject', value);
        } else {
            // 如果没有id,则挂载时利用缓存的值进行查询
            this.isQueryOnMount = true;
        }
    },
    methods: {
        /**
         * 选择后的回调
         * @param {Array} value  选中的值
         */
        handleChange(value) {
            this.projectId = value[value.length - 1];
        },
        /**
         * 返回
         */
        goback() {
            history.go(-1);
        }
    }
};
</script>

<style lang="scss" scoped>
.finance-container {
    overflow: auto;
    height: 100vh;
}
.tabs {
    margin: 10px 10px 0 16px;
}
.goback-button {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 100;
}
</style>
