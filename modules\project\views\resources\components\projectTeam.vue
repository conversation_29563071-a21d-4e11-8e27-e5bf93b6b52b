<template>
    <div>
        <div class="proTeam-box">
            <div class="table-search" v-if="personnelData.length != 0">
                <el-button
                    type="primary"
                    size="mini"
                    icon="el-icon-help"
                    @click="saveChange()"
                    v-if="showProjectTeamSaveBtnpermission"
                    >保存</el-button
                >
                <el-button
                    type="primary"
                    size="mini"
                    icon="el-icon-s-data"
                    v-if="showSyncZentaoProjectBtn"
                    @click="synchro()"
                >
                    同步禅道</el-button
                >
            </div>
            <div
                class="proTeam-line"
                v-for="(item, index) in personnelData"
                :key="index"
            >
                <div class="pro-position">{{ item.position }}</div>
                <div class="pro-position">
                    <people-selector
                        v-model="item.leader"
                        placeholder="请输入负责人"
                        @input="handleleaderChange(personnelData, item)"
                        :isMultipled="item.position !== '项目经理'"
                    ></people-selector>
                </div>
                <div
                    class="pro-gcs"
                    v-if="
                        item.position !== '项目经理' && item.position !== 'PQA'
                    "
                >
                    {{ item.positionTitle }}
                </div>
                <div class="pro-staff">
                    <people-selector
                        v-if="
                            item.position !== '项目经理' &&
                            item.position !== 'PQA'
                        "
                        v-model="item.personnel"
                        placeholder="请输入组员"
                        @input="handlePersonnelChange(personnelData, item)"
                    ></people-selector>
                </div>
            </div>
            <div class="proTeam-bottom" v-if="personnelData.length !== 0">
                <div>其他资源部门人员</div>
                <div class="pro-staff">
                    <people-selector
                        style="width: 100%; margin-top: 3px"
                        :value="otherDepartmentPerson"
                        @input="hanldeOtherPersonChange"
                        placeholder="其他资源部门人员只允许删除，不允许新增"
                        :options="otherDepartmentOptions"
                        :disabled="otherDepartmentOptions.length === 0"
                    ></people-selector>
                </div>
            </div>
        </div>
        <div v-if="personnelData.length === 0" class="proTeam-show">
            暂无数据
        </div>
    </div>
</template>

<script>
import PeopleSelector from 'Components/PeopleSelector';

export default {
    components: { PeopleSelector },
    props: {
        personnelData: {
            type: Array,
            required: true
        },
        otherpersonnel: {
            type: Array,
            required: true
        },
        otherDepartmentOptions: {
            type: Array,
            default: () => []
        }
    },
    computed: {
        showProjectTeamSaveBtnpermission() {
            return this.$store.state.permission.btnDatas.includes(
                'ProjectTeamSaveBtn'
            );
        },
        showSyncZentaoProjectBtn() {
            return this.$store.state.permission.btnDatas.includes(
                'SyncZentaoProjectBtn'
            );
        },
        otherDepartmentPerson: {
            get() {
                return this.otherpersonnel;
            },
            set(newValue) {
                this.$emit('update:otherpersonnel', newValue);
            }
        }
    },
    data() {
        return {
            loading: false
        };
    },
    methods: {
        handlePersonnelChange(data, item) {
            this.$emit('handle-personnel-change', data, item);
        },
        handleleaderChange(data, item) {
            this.$emit('handle-leader-change', data, item);
        },
        saveChange() {
            this.$emit('save-change');
        },
        synchro() {
            this.$emit('synchro-cd');
        },
        hanldeOtherPersonChange(value) {
            this.$emit('update:otherpersonnel', value);
        }
    }
};
</script>
<style lang="scss" scoped>
.proTeam-box {
    width: 100%;
    height: 100%;
    padding: 10px 20px 20px 20px;
}
.table-search {
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: flex-end;
}
.el-button {
    height: 35px;
    font-weight: bolder;
}
.proTeam-line {
    width: 100%;
    display: flex;
    align-items: center;
    padding: 5px 0px 5px 0px;
}
.pro-position,
.pro-staff,
.pro-gcs {
    height: 100%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    font-weight: 500;
    font-size: 14px;
}
.pro-position {
    width: 20%;
}
.pro-gcs {
    width: 20%;
    padding-left: 5%;
}
.pro-staff {
    width: 40%;
}
.custom-select,
.proTeam-bottom {
    width: 100%;
}
.proTeam-bottom {
    display: flex;
    align-items: center;
}
.proTeam-bottom div:first-child {
    width: 20%;
    height: 100%;
    display: flex;
    justify-content: flex-start;
}
.proTeam-bottom div:nth-child(2) {
    width: 80%;
}
.proTeam-show {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
}
</style>
