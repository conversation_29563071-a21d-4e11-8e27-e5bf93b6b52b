<template>
    <div class="maintenance-defect-detail">
        <div class="flex">
            <h3>缺陷详情</h3>
            <div>
                <el-button
                    type="primary"
                    v-permission="['maintenanceProjectDefectUpdate']"
                    @click="handleEdit"
                    >编辑</el-button
                >
                <el-button type="primary" @click="goBack">返回</el-button>
            </div>
        </div>

        <!-- 基本信息 -->
        <el-divider>基本信息</el-divider>
        <el-descriptions :column="4" border>
            <el-descriptions-item label="缺陷标题" :span="4">{{
                defectDetail.faultTitle
            }}</el-descriptions-item>
            <el-descriptions-item label="缺陷状态">{{
                defectDetail.faultStatus
            }}</el-descriptions-item>
            <el-descriptions-item label="项目经理">{{
                defectDetail.projectManagerName
            }}</el-descriptions-item>
            <el-descriptions-item
                v-if="defectDetail.faultSource === '电子流（顾客投诉OA）'"
                label="关联客户"
                :span="2"
                >{{ defectDetail.customerName }}</el-descriptions-item
            >
            <el-descriptions-item label="缺陷来源">{{
                defectDetail.faultSource
            }}</el-descriptions-item>
            <el-descriptions-item
                v-if="defectDetail.faultSource === '电子流（顾客投诉OA）'"
                label="OA流程状态"
                >{{ defectDetail.flowStatus }}</el-descriptions-item
            >
            <el-descriptions-item
                v-if="defectDetail.faultSource === '电子流（顾客投诉OA）'"
                label="关联流程ID"
                :span="2"
                >{{ defectDetail.flowNo }}</el-descriptions-item
            >
            <el-descriptions-item label="产品型号">{{
                defectDetail.productModel
            }}</el-descriptions-item>
            <el-descriptions-item label="产品名称">{{
                defectDetail.productName
            }}</el-descriptions-item>
            <el-descriptions-item label="归属产品线">{{
                defectDetail.productLine
            }}</el-descriptions-item>
            <el-descriptions-item label="归属细分产品线">{{
                defectDetail.subProductLine
            }}</el-descriptions-item>
        </el-descriptions>

        <el-divider>缺陷识别</el-divider>
        <el-descriptions :column="4" border class="mt-20">
            <el-descriptions-item label="发现时间">{{
                defectDetail.applyTime
            }}</el-descriptions-item>
            <el-descriptions-item label="缺陷严重等级">{{
                defectDetail.faultLevel
            }}</el-descriptions-item>
            <template
                v-if="defectDetail.faultSource === '电子流（顾客投诉OA）'"
            >
                <el-descriptions-item label="问题定义">{{
                    defectDetail.problemDefinition
                }}</el-descriptions-item>
            </template>
            <el-descriptions-item
                label="缺陷占比"
                :span="
                    defectDetail.faultSource === '电子流（顾客投诉OA）' ? 1 : 2
                "
                >{{
                    defectDetail.faultProp ? `${defectDetail.faultProp}%` : ''
                }}</el-descriptions-item
            >
            <el-descriptions-item label="缺陷描述" :span="4">{{
                defectDetail.problemOverview
            }}</el-descriptions-item>
        </el-descriptions>

        <el-divider>缺陷分析与应对</el-divider>
        <el-descriptions :column="4" border class="mt-20">
            <el-descriptions-item label="根因分析" :span="4">{{
                defectDetail.causeAnalyse
            }}</el-descriptions-item>
            <el-descriptions-item label="临时解决措施" :span="4">{{
                defectDetail.solutionMeasuresTemp
            }}</el-descriptions-item>
            <el-descriptions-item label="永久解决措施" :span="4">{{
                defectDetail.solutionMeasures
            }}</el-descriptions-item>
            <el-descriptions-item label="已上升技术委员会" :span="4">{{
                defectDetail.whetherAscendTechCommittee
            }}</el-descriptions-item>
            <el-descriptions-item label="解决人" :span="4">{{
                defectDetail.resolvePersonList &&
                defectDetail.resolvePersonList.join('，')
            }}</el-descriptions-item>
        </el-descriptions>

        <template v-if="defectDetail.proProjectId">
            <el-divider>缺陷解决计划</el-divider>
            <ZentaoTaskList
                class="mt-10"
                :show="isShowZentaoTaskList"
                :proProjectId="defectDetail.proProjectId"
                :proTaskId="defectDetail.proTaskId"
                :projectManagerAccount="projectManagerAccount"
            ></ZentaoTaskList>
        </template>
        <div v-show="defectDetail.hasRisk === '是'">
            <el-divider>影响缺陷解决的风险</el-divider>
            <RiskList
                class="mt-10"
                :riskListCounter="riskListCounter"
                :assObjectId="id"
                relatedObject="维护项目缺陷"
            ></RiskList>
        </div>
        <DefectUpdateDialog
            type="edit"
            :visible.sync="defectUpdateDialogVisible"
            :id="id"
            @success="getDetail"
        ></DefectUpdateDialog>
    </div>
</template>

<script>
import ZentaoTaskList from 'maintenanceProject/components/ZentaoTaskList';
import RiskList from 'maintenanceProject/components/RiskList';
import DefectUpdateDialog from '../DefectList/DefectUpdateDialog';

const formData = {
    faultTitle: '',
    faultStatus: '',
    projectManagerName: '',
    faultSource: '',
    flowStatus: '',
    flowNo: '',
    productModel: '',
    productName: '',
    productLine: '',
    subProductLine: '',
    applyTime: '',
    faultLevelList: '',
    problemDefinition: '',
    faultProp: '',
    problemOverview: '',
    causeAnalyse: '',
    solutionMeasures: '',
    customerName: '',
    resolvePersonList: []
};

export default {
    name: 'MaintenanceDefectDetail',
    components: {
        ZentaoTaskList,
        RiskList,
        DefectUpdateDialog
    },
    data() {
        return {
            defectDetail: formData,
            // 缺陷ID
            id: '',
            // 禅道项目ID
            proProjectId: null,
            // 项目经理域账号
            projectManagerAccount: '',
            defectUpdateDialogVisible: false,
            isShowZentaoTaskList: false,
            riskListCounter: 0
        };
    },
    created() {
        const { query = null } = this.$route;
        if (query) {
            this.id = query.defect_id;
            this.getDetail();
        }
        this.closeAllPopovers();
    },
    methods: {
        goBack() {
            this.$router.go(-1);
        },
        handleEdit() {
            this.defectUpdateDialogVisible = true;
        },
        /**
         * 获取缺陷详情
         */
        async getDetail() {
            if (!this.id) return;
            const api = this.$service.maintenanceProject.defect.getDetail;
            const params = {
                id: this.id,
                // 流程软硬件归属，固定为硬件
                flowSoftHardBelong: '硬件'
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.defectDetail = res.body;
                    this.projectManagerAccount =
                        this.defectDetail.projectManager;
                    this.proProjectId = this.defectDetail.proProjectId;
                    this.isShowZentaoTaskList = true;
                    if (this.defectDetail.hasRisk === '是') {
                        this.riskListCounter += 1;
                    }
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 关闭所有弹窗
         */
        closeAllPopovers() {
            this.$root.$emit('close-popovers');

            const popovers = document.querySelectorAll('.el-popover');
            popovers.forEach((popover) => {
                if (popover.style.display !== 'none') {
                    document.body.click();
                    popover.style.display = 'none';
                }
            });

            const tooltipPopovers = document.querySelectorAll(
                '.el-tooltip__popper'
            );
            tooltipPopovers.forEach((tooltipPopover) => {
                if (tooltipPopover.style.display !== 'none') {
                    document.body.click();
                    tooltipPopover.style.display = 'none';
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.maintenance-defect-detail {
    padding: 20px;
    height: calc(100vh - 20px);
    overflow: auto;
}
.flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.mt-10 {
    margin-top: 10px;
}
.mt-20 {
    margin-top: 20px;
}
::v-deep .el-descriptions-item__label {
    width: 120px;
}
</style>
