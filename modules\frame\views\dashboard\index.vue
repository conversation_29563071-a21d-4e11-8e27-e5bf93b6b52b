<template>
    <div style="padding: 8px 20px 20px 20px; position: relative">
        <el-tabs v-model="activeName">
            <el-tab-pane label="首页" name="dashBoard">
                <div class="container" style="height: calc(100vh - 150px)">
                    <h1>{{ $t('frame.dashboard.welcome') }}{{ title }}</h1>
                </div></el-tab-pane
            >
            <el-tab-pane
                label="业务板块"
                name="businessSector"
                lazy
                v-if="showBusinessSector"
            >
                <BusinessSector></BusinessSector>
            </el-tab-pane>
            <el-tab-pane label="我的会议" name="myMeetings" lazy>
                <span slot="label"> 我的会议 </span>
                <MyMeetings :activeName="activeName"></MyMeetings>
            </el-tab-pane>
            <el-tab-pane
                label="个人负载"
                name="personalLoad"
                lazy
                v-if="showPersonalLoad"
            >
                <PersonResourceLoad
                    :loginName="loginName"
                    :userName="userName"
                    :date="date"
                    :inputView="view"
                ></PersonResourceLoad>
            </el-tab-pane>
        </el-tabs>
        <el-button
            type="primary"
            @click="goBack"
            v-show="showReturnButton"
            style="position: absolute; right: 20px; top: 14px"
            >返回</el-button
        >
    </div>
</template>

<script>
import PersonResourceLoad from './components/personalLoad/personResourceLoad.vue';
import storage from 'wtf-core-vue/src/methods/storage';
import BusinessSector from 'scene/views/scene/components/entrance/BusinessSector';
import MyMeetings from 'feature/views/meetingManagement/components/myMeetings';
import { getAllEmployeeList, getProductLine } from './common';

export default {
    components: {
        PersonResourceLoad,
        BusinessSector,
        MyMeetings
    },
    data() {
        return {
            // 当前页签的位置
            activeName: 'dashBoard',
            // 域账号
            loginName: '',
            // 姓名
            userName: '',
            // 选择的日期
            date: '',
            // 视图
            view: '',
            // 是否展示返回按钮
            showReturnButton: false,
            // 个人负载权限控制
            showPersonalLoad:
                this.$store.state.permission.btnDatas.includes(
                    'personalLoadTab'
                ),
            // 业务板块（一体两翼）权限控制
            showBusinessSector:
                this.$store.state.permission.btnDatas.includes(
                    'businessSectorTab'
                )
        };
    },
    computed: {
        title() {
            return this.$t(this.$store.state.settings.title);
        }
    },
    watch: {
        // eslint-disable-next-line object-shorthand
        $route: function (to, from) {
            if (to.query) {
                if (from.query) {
                    if (to.query.time !== from.query) {
                        location.reload();
                    }
                } else {
                    location.reload();
                }
            }
        }
    },
    created() {
        // 如果没有一体两翼（业务板块）页签权限，默认进入我的会议
        if (this.showBusinessSector) {
            this.activeName = 'businessSector';
        } else {
            this.activeName = 'myMeetings';
        }
        // 如果有传入activeName，使用传入的
        if (this.$route?.query?.activeName) {
            this.activeName = this.$route.query.activeName;
        }
        // 进入首页时，查询全部人员名单（包含离职）与在职人员名单
        getAllEmployeeList(0, this);
        getAllEmployeeList(1, this);
        // 获取产品线
        getProductLine(this);
        if (this.$store.state?.user?.userInfo?.jobNumber) {
            storage.setLocalStorage(
                'loginName',
                this.$store.state.user.userInfo.jobNumber
            );
            storage.setLocalStorage('userName', this.$store.state.user.name);
        }
        if (this.$route?.query?.name) {
            this.showReturnButton = true;
        }
        // 需要查看资源负载时
        if (this.$route?.query?.name) {
            this.activeName = 'personalLoad';
            this.loginName = this.$route?.query?.name;
            this.date = this.$route?.query?.date;
            this.view = this.$route?.query?.view;
            this.userName = this.$store.state.frame.resourceName;
            this.showPersonalLoad = true;
        } else {
            this.loginName = storage.getLocalStorage('loginName');
            this.userName = storage.getLocalStorage('userName');
        }
    },
    methods: {
        goBack() {
            history.go(-1);
        }
    }
};
</script>

<style lang="scss" scoped>
.container {
    display: flex;
    align-items: center;
    justify-content: center;
}
.flex {
    display: flex;
}
</style>
