<!-- 会议任务追踪 -->
<template>
    <div class="meeting-track-container">
        <div class="flex">
            <el-checkbox-group
                class="query-container"
                v-model="type"
                @change="handleCheckboxChange"
            >
                <el-checkbox :label="1">我组织的</el-checkbox>
                <el-checkbox :label="2">我参加的</el-checkbox>
                <el-checkbox :label="3">我提出过要求的</el-checkbox>
                <el-checkbox :label="4">会议任务责任人是我的</el-checkbox>
            </el-checkbox-group>
            <div class="button-group">
                <el-button class="action-button" type="primary" @click="getList"
                    >查询</el-button
                >
                <el-button
                    class="action-button"
                    type="primary"
                    @click="handleReset"
                    >重置</el-button
                >
            </div>
        </div>
        <div class="flex">
            <div class="query-container">
                <el-select
                    class="query"
                    v-model="projectId"
                    placeholder="关联项目"
                    style="width: 300px"
                    remote
                    :remote-method="remoteMethod"
                    filterable
                    clearable
                >
                    <el-option
                        v-for="item in searchOptions"
                        :key="item.projectId"
                        :label="item.projectName"
                        :value="item.projectId"
                    >
                    </el-option>
                </el-select>
                <el-select
                    class="query task-status"
                    v-model="taskStatus"
                    placeholder="任务状态"
                    clearable
                >
                    <el-option
                        v-for="item in CONSTANTS.TASK_STATUS"
                        :key="item"
                        :label="item"
                        :value="item"
                    >
                    </el-option>
                </el-select>
                <el-date-picker
                    v-model="daterange"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="任务计划完成开始日期"
                    end-placeholder="任务计划完成结束日期"
                    value-format="yyyy-MM-dd"
                >
                </el-date-picker>

                <el-input
                    class="query"
                    v-model="meetingRequire"
                    placeholder="会议要求关键字"
                ></el-input>
            </div>
        </div>
        <el-table
            class="meeting-trace-table"
            :data="tableList"
            :header-cell-style="{
                'text-align': 'center',
                'border': '1px solid #8c8c8c'
            }"
            :cell-style="{
                'vertical-align': 'top',
                'border': '1px solid #8c8c8c!important'
            }"
            height="calc(100vh - 220px)"
            empty-text="无会议任务数据"
            :span-method="objectSpanMethod"
        >
            <el-table-column
                prop="meetingTitle"
                label="会议名称"
                width="200"
                align="left"
            >
            </el-table-column>
            <el-table-column
                prop="problemItem"
                label="问题/事项"
                width="280"
                align="left"
            >
            </el-table-column>
            <el-table-column
                align="center"
                prop="creatorName"
                label="提出人"
                width="80"
            >
            </el-table-column>
            <el-table-column prop="meetingRequire" label="会议要求" width="280">
            </el-table-column>
            <el-table-column
                prop="responsibleName"
                label="责任人"
                align="center"
                width="80"
            >
            </el-table-column>
            <el-table-column
                prop="planFinishDate"
                label="计划完成时间"
                align="center"
                width="100"
            >
            </el-table-column>
            <el-table-column
                prop="finishStatus"
                label="任务状态"
                align="center"
                width="100"
            >
            </el-table-column>
            <el-table-column prop="finishDesc" label="完成情况" width="280">
            </el-table-column>
            <el-table-column
                label="操作"
                align="center"
                width="90"
                fixed="right"
            >
                <template slot-scope="scope">
                    <el-button type="primary" @click="handleEdit(scope.row)"
                        >编辑</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <pagination
            class="pagination"
            :total="total"
            :page.sync="page"
            :limit.sync="limit"
            @pagination="getList"
        />
        <MeetingTraceTaskDialog
            :visible.sync="dialogVisible"
            :data="traceTaskData"
            @update="getList"
        ></MeetingTraceTaskDialog>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants.js';
import Pagination from 'wtf-core-vue/src/components/Pagination';
import { getUserAccount } from '../../commonFunction';
import MeetingTraceTaskDialog from './meetingTraceTaskDialog.vue';

export default {
    name: 'MeetingTaskTrace',
    components: { Pagination, MeetingTraceTaskDialog },
    data() {
        return {
            dialogVisible: false,
            pickerOptions: {
                firstDayOfWeek: 1
            },
            // 时间范围
            daterange: [],
            // 和我相关的会议1-组织，2-参加，3-提出要求，4-任务责任人
            type: [4],
            // 会议类型
            meetingType: '',
            // 会议名称
            meetingTitle: '',
            // 关联项目ID
            projectId: '',
            // 所有关联项目
            relatedProjectsOptions: [],
            searchOptions: [],
            // 会议要求关键字
            meetingRequire: '',
            // 当前登录用户域账号
            myAccount: '',
            // 责任人部门
            orgCode: '',
            // 任务状态
            taskStatus: '未完成',
            // 会议跟踪任务数据
            traceTaskData: {},
            CONSTANTS,
            tableList: [],
            dateArr: [],
            total: 0,
            page: 1,
            limit: 50
        };
    },
    created() {
        this.getRelatedProjectsOptions();
        this.getList();
    },
    activated() {
        this.getRelatedProjectsOptions();
        this.getList();
    },
    methods: {
        handleReset() {
            this.daterange = [];
            this.meetingRequire = '';
            this.type = [4];
            this.projectId = '';
            this.taskStatus = '未完成';
            this.page = 1;
            this.limit = 50;
            this.getList();
        },
        async getList() {
            const api = this.$service.feature.meetingTaskTrace.getList;
            const params = {
                currentPage: this.page,
                startDate: this.daterange ? this.daterange[0] : '',
                endDate: this.daterange ? this.daterange[1] : '',
                myAccount: getUserAccount(),
                pageSize: this.limit,
                meetingRequire: this.meetingRequire,
                type: this.type[0] || '',
                projectId: this.projectId,
                taskStatus: this.taskStatus
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.tableList = res.body.list;
                    this.total = res.body.total;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        handleCheckboxChange(value) {
            if (this.type.length > 1) {
                this.type.splice(0, 1);
            }
        },
        /**
         * 获取所有项目用于下拉框选项
         */
        async getRelatedProjectsOptions() {
            try {
                const api =
                    this.$service.department.naturalResources.getProjectselect;
                const res = await api();
                if (res.head.code === '000000') {
                    this.relatedProjectsOptions = res.body || [];
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (err) {
                console.error(err);
            }
        },
        handleEdit(data) {
            this.dialogVisible = true;
            this.traceTaskData = data;
        },
        /**
         * 远程搜索，解决过量数据卡顿问题
         * @param {Object} query 参数
         */
        remoteMethod(query) {
            if (query !== '') {
                this.searchOptions = this.relatedProjectsOptions.filter(
                    (item) => {
                        return item.projectName.indexOf(query) > -1;
                    }
                );
            } else {
                this.searchOptions = [];
            }
        },
        /**
         * 合并单元格
         * @param {Object} param 表格数据
         * @returns {Function} 合并单元格
         */
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            // 只处理会议名称、事项/问题、提出人、会议要求的合并
            if (![0, 1, 2, 3].includes(columnIndex)) {
                return {
                    rowspan: 1,
                    colspan: 1
                };
            }

            // 根据列确定要比较的字段
            const getCompareField = (colIndex) => {
                switch (colIndex) {
                    case 0:
                        return 'meetingTitle';
                    case 1:
                        return 'problemItem';
                    case 2:
                        return 'creatorName';
                    case 3:
                        return 'meetingRequire';
                    default:
                        return '';
                }
            };

            const compareField = getCompareField(columnIndex);
            if (rowIndex === 0) {
                let count = 1;
                for (let i = 1; i < this.tableList.length; i++) {
                    if (
                        this.tableList[i][compareField] === row[compareField] &&
                        this.tableList[i].meetingId === row.meetingId
                    ) {
                        count += 1;
                    } else {
                        break;
                    }
                }
                return {
                    rowspan: count,
                    colspan: 1
                };
            }

            // 与上一行比较，判断是否需要合并
            const prevRow = this.tableList[rowIndex - 1];
            // 必须是相同会议才会进行合并
            if (
                prevRow[compareField] === row[compareField] &&
                prevRow.meetingId === row.meetingId
            ) {
                return {
                    rowspan: 0,
                    colspan: 0
                };
            }

            // 计算当前行需要合并的行数
            let count = 1;
            for (let i = rowIndex + 1; i < this.tableList.length; i++) {
                if (
                    this.tableList[i][compareField] === row[compareField] &&
                    this.tableList[i].meetingId === row.meetingId
                ) {
                    count += 1;
                } else {
                    break;
                }
            }

            return {
                rowspan: count,
                colspan: 1
            };
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.meeting-track-container {
    height: calc(100vh - 65px);
}

.query-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.query {
    width: 220px;
}
.task-status {
    width: 120px;
}

@media (max-width: 568px) {
    .query {
        flex: 1 1 100%;
    }
}
// 修改placeholder颜色
::v-deep .query .el-input__inner::placeholder {
    color: rgba(0, 0, 0, 0.685);
}
.qucik-access {
    margin-left: 10px;
}

.button-group {
    margin-left: auto;
}

.action-button {
    height: 28px;
    margin-left: 15px;
}

.meeting-trace-table {
    margin-top: 10px;
    border: 1px solid #8c8c8c !important;
}

.pagination {
    padding: 5px 0;
    margin: 10px 0 0 0;
}

.meeting-trace-table .meeting-title-button {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    padding: 0px !important;
    max-width: 100%;
    height: 23px;
}

// 统一表头高度，修正固定列错位
::v-deep .el-table__header {
    padding: 0;
    height: 50px !important;
}

// 调整table表头颜色
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border-bottom: 1px solid #dfe6ec !important;
}
</style>
