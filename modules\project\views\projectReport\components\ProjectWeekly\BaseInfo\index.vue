<template>
    <div class="base-info">
        <el-descriptions :column="4" border>
            <template #title>
                <div class="title-container">
                    <div class="title">项目基本信息</div>
                </div>
            </template>
            <el-descriptions-item label="项目状态">
                <span>{{ projectInfo.projectStatus }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="进度状态">
                <span class="status-item" v-if="projectInfo.progressStatus">
                    <i
                        class="status-dot"
                        :class="
                            projectInfo.progressStatus === '正常'
                                ? 'status-normal'
                                : 'status-delay'
                        "
                    ></i>
                    {{ projectInfo.progressStatus }}
                </span>
            </el-descriptions-item>
            <el-descriptions-item label="需提供支持的风险数量">
                {{ projectInfo.supportRiskCount }}
            </el-descriptions-item>
            <el-descriptions-item label="预算执行进度">
                <budget-progress
                    v-if="projectInfo.budgetExecutionRatio"
                    :percentage="projectInfo.budgetExecutionRatio"
                />
                <span v-else>未提交预算</span>
            </el-descriptions-item>

            <el-descriptions-item label="项目编号">
                {{ projectInfo.projectNumber }}
            </el-descriptions-item>
            <el-descriptions-item label="产品型号">
                {{ projectInfo.productModel }}
            </el-descriptions-item>
            <el-descriptions-item label="项目经理">
                {{ projectInfo.projectManager }}
            </el-descriptions-item>
            <el-descriptions-item label="产品经理">
                {{ projectInfo.productManager }}
            </el-descriptions-item>
        </el-descriptions>
    </div>
</template>

<script>
import BudgetProgress from '../../common/BudgetProgress.vue';

export default {
    name: 'BaseInfo',
    components: {
        BudgetProgress
    },
    props: {
        weekReportId: {
            type: String,
            default: ''
        },
        projectInfo: {
            type: Object,
            default: () => ({})
        }
    },
    data() {
        return {};
    }
};
</script>

<style lang="scss" scoped>
@import 'project/views/projectReport/components/common/common.scss';

.title {
    @include section-title;
}
.title-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    border-bottom: 1px solid #8c8c8c;
    height: 28px;
}
.base-info {
    width: 100%;

    .status-item {
        display: flex;
        align-items: center;
    }

    .status-dot {
        display: inline-block;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 6px;

        &.status-normal {
            background-color: #67c23a;
        }

        &.status-delay {
            background-color: #f56c6c;
        }
    }
}
::v-deep .el-descriptions__title {
    width: 100%;
}
::v-deep tr.el-descriptions-row {
    height: 36px;
}
</style>
