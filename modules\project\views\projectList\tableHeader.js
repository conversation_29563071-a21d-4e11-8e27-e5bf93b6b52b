/* eslint-disable max-lines-per-function */

const getDetail = (scope) => {
    return (row) => {
        scope.$router.push({
            path: '/project/baseInfo',
            query: {
                id: row.projectId
            }
        });
    };
};
const elButtonFn = () => {
    // 去除button的padding，设置max-width，否则tooltip与省略号不会正常显示
    return {
        type: 'text',
        style: 'white-space: nowrap; overflow: hidden; text-overflow: ellipsis; text-align:left; padding:0px!important; max-width:100%;'
    };
};
// 市场待立项
const pending = (scope) => {
    return [
        {
            label: '项目名称',
            prop: 'projectName',
            show: true,
            minWidth: 250,
            renderMode: 'button',
            elButtonAttrsFn: elButtonFn,
            handleClick: getDetail(scope),
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '产品线',
            prop: 'productLine',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '类型',
            prop: 'projectApproval',
            show: true,
            minWidth: 80,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        },
        {
            label: '启动时间',
            prop: 'startDate',
            show: true,
            minWidth: 80,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '项目经理',
            prop: 'projectManager',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        }
    ];
};
// 排队中
const queued = (scope) => {
    return [
        {
            label: '项目名称',
            prop: 'projectName',
            show: true,
            minWidth: 250,
            renderMode: 'button',
            elButtonAttrsFn: elButtonFn,
            handleClick: getDetail(scope),
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '产品线',
            prop: 'productLine',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '项目类型',
            prop: 'projectApproval',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        },
        {
            label: '产品型号',
            prop: 'productModeNo',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '市场决议函下达时间',
            prop: 'marketResoluteIssueDate',
            show: true,
            minWidth: 160,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '已下达天数',
            prop: 'issueDays',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '未启动原因',
            prop: 'queuingNotStartingReason',
            show: true,
            minWidth: 160,
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '市场需求导入实例号',
            prop: 'marketStoryImportFlowInstanceNo',
            show: true,
            minWidth: 200,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '产品经理',
            prop: 'productManager',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        },
        {
            label: '项目经理',
            prop: 'projectManager',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        }
    ];
};
// 进行中
const onGoing = (scope) => {
    return [
        {
            label: '项目编号',
            prop: 'projectNumber',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '项目名称',
            prop: 'projectName',
            show: true,
            minWidth: 250,
            renderMode: 'button',
            elButtonAttrsFn: elButtonFn,
            handleClick: getDetail(scope),
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '产品线',
            prop: 'productLine',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '项目类型',
            prop: 'projectApproval',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        },
        {
            label: '产品型号',
            prop: 'productModeNo',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '启动时间',
            prop: 'startDate',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '难度等级',
            prop: 'projectTechnicalDifficulty',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        },
        {
            label: '所处阶段',
            prop: 'projectStage',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        },
        {
            label: '当前里程碑',
            prop: 'currentProjectDetailName',
            show: true,
            minWidth: 180,
            elTableColumnAttrs: {
                resizable: false
            },
            slot: 'currentMilestone'
        },
        {
            label: '当前里程碑计划时间',
            prop: 'currentProjectDetailPlanDate',
            show: true,
            minWidth: 140,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '当前进度状态',
            prop: 'currentScheduleStatus',

            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        },
        {
            label: '产品经理',
            prop: 'productManager',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        },
        {
            label: '项目经理',
            prop: 'projectManager',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        }
    ];
};
// 已暂停
const paused = (scope) => {
    return [
        {
            label: '项目编号',
            prop: 'projectNumber',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '项目名称',
            prop: 'projectName',
            show: true,
            minWidth: 250,
            renderMode: 'button',
            elButtonAttrsFn: elButtonFn,
            handleClick: getDetail(scope),
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '产品线',
            prop: 'productLine',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '项目类型',
            prop: 'projectApproval',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        },
        {
            label: '产品型号',
            prop: 'productModeNo',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '启动时间',
            prop: 'startDate',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '状态变更实例号',
            prop: 'oaFlowId',
            show: true,
            minWidth: 220,
            elTableColumnAttrs: {
                resizable: false
            },
            slot: 'pausedOaFlowId'
        },
        {
            label: '暂停时间',
            prop: 'stopDate',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '暂停前已完成里程碑',
            prop: 'completedDetailName',
            show: true,
            minWidth: 180,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '暂停前的目标里程碑',
            prop: 'targetDetailName',
            show: true,
            minWidth: 180,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '产品经理',
            prop: 'productManager',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        },
        {
            label: '项目经理',
            prop: 'projectManager',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        }
    ];
};
// 已终止
const terminated = (scope) => {
    return [
        {
            label: '项目编号',
            prop: 'projectNumber',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '项目名称',
            prop: 'projectName',
            show: true,
            minWidth: 250,
            renderMode: 'button',
            elButtonAttrsFn: elButtonFn,
            handleClick: getDetail(scope),
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '产品线',
            prop: 'productLine',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '项目类型',
            prop: 'projectApproval',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        },
        {
            label: '产品型号',
            prop: 'productModeNo',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '启动时间',
            prop: 'startDate',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '状态变更实例号',
            prop: 'oaFlowId',
            show: true,
            minWidth: 220,
            elTableColumnAttrs: {
                resizable: false
            },
            slot: 'terminatedOaFlowId'
        },
        {
            label: '终止时间',
            prop: 'stopDate',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '终止前已完成里程碑',
            prop: 'completedDetailName',
            show: true,
            minWidth: 180,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '终止前的目标里程碑',
            prop: 'targetDetailName',
            show: true,
            minWidth: 180,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '产品经理',
            prop: 'productManager',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        },
        {
            label: '项目经理',
            prop: 'projectManager',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        }
    ];
};
// 已结项
const completed = (scope) => {
    return [
        {
            label: '项目编号',
            prop: 'projectNumber',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '项目名称',
            prop: 'projectName',
            show: true,
            minWidth: 250,
            renderMode: 'button',
            elButtonAttrsFn: elButtonFn,
            handleClick: getDetail(scope),
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '产品线',
            prop: 'productLine',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '项目类型',
            prop: 'projectApproval',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        },
        {
            label: '产品型号',
            prop: 'productModeNo',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '启动时间',
            prop: 'startDate',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '结项前已完成里程碑',
            prop: 'completedDetailName',
            show: true,
            minWidth: 180,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '结项里程碑完成时间',
            prop: 'completedDetailDate',
            show: true,
            minWidth: 140,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '难度等级',
            prop: 'projectTechnicalDifficulty',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        },
        {
            label: '项目周期',
            prop: 'projectCycle',
            show: true,
            minWidth: 80,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '产品经理',
            prop: 'productManager',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        },
        {
            label: '项目经理',
            prop: 'projectManager',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        }
    ];
};
// 全部
const all = (scope) => {
    return [
        {
            label: '项目编号',
            prop: 'projectNumber',
            show: true,
            minWidth: 100,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '项目名称',
            prop: 'projectName',
            show: true,
            minWidth: 250,
            renderMode: 'button',
            elButtonAttrsFn: elButtonFn,
            handleClick: getDetail(scope),
            elTableColumnAttrs: {
                'resizable': false,
                'header-align': 'center',
                'align': 'left'
            }
        },
        {
            label: '产品线',
            prop: 'productLine',
            show: true,
            minWidth: 120,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '类型',
            prop: 'projectApproval',
            show: true,
            minWidth: 80,
            elTableColumnAttrs: {
                sortable: 'custom',
                resizable: false
            }
        },
        {
            label: '项目经理',
            prop: 'projectManager',
            show: true,
            minWidth: 80,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '启动时间',
            prop: 'startDate',
            show: true,
            minWidth: 80,
            elTableColumnAttrs: {
                resizable: false
            }
        },
        {
            label: '状态',
            prop: 'projectStatus',
            show: true,
            minWidth: 80,
            elTableColumnAttrs: {
                resizable: false
            }
        }
    ];
};
const getStatusArray = (index, scope) => {
    const statusMap = [
        pending(scope),
        queued(scope),
        onGoing(scope),
        paused(scope),
        terminated(scope),
        completed(scope),
        all(scope)
    ];
    return statusMap[index];
};
export { getStatusArray };
