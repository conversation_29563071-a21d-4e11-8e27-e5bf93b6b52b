<template>
    <div class="defect-list-container">
        <div class="flex">
            <CollapsibleSearchPanel
                :queryParams="queryParams"
                :queryConfig="queryConfig"
                :isDot="isDot"
                :navItems="navItems"
                v-model="navActiveName"
                @navChange="handleNavChange"
                @search="handleSearch"
                @reset="handleReset"
            >
                <template #rightNav>
                    <el-button
                        type="primary"
                        @click="addRisk"
                        :disabled="
                            (projectType === 'develop' && !projectId) ||
                            createTaskByStatusDisabled
                        "
                        v-permission="['maintenanceProjectRiskUpdate']"
                        >新建风险</el-button
                    >
                </template>
            </CollapsibleSearchPanel>
        </div>
        <div class="content-area">
            <el-table :data="tableData" height="calc(100vh - 230px)">
                <el-table-column
                    prop="index"
                    label="序号"
                    align="center"
                    width="60"
                ></el-table-column>
                <el-table-column
                    prop="riskIdentifyDate"
                    label="风险识别日期"
                    align="center"
                    width="110"
                ></el-table-column>
                <el-table-column
                    prop="riskId"
                    label="风险编号"
                    align="center"
                    width="140"
                ></el-table-column>
                <el-table-column label="风险标题" align="center">
                    <template slot-scope="scope">
                        <el-tooltip
                            effect="dark"
                            :content="scope.row.riskTitle"
                            placement="top"
                            popper-class="maintananceProject-tooltip-width"
                        >
                            <a
                                @click="handleRoutingJump(scope.row)"
                                class="overview-ellipsis"
                            >
                                {{ scope.row.riskTitle }}
                            </a>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="riskAssObjectType"
                    label="关联对象"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    prop="riskLevel"
                    label="风险级别"
                    align="center"
                    width="80"
                ></el-table-column>
                <el-table-column
                    prop="riskStatus"
                    label="风险状态"
                    align="center"
                    width="90"
                ></el-table-column>
                <el-table-column
                    prop="projectManagerName"
                    label="项目经理"
                    align="center"
                    width="90"
                ></el-table-column>
                <el-table-column label="操作" align="center" width="120">
                    <template slot-scope="scope">
                        <div
                            class="flex flex-center gap-5"
                            style="height: 31px"
                        >
                            <!-- 关联禅道项目 -->
                            <el-popover
                                v-if="scope.row.proTaskId"
                                class="mt-5"
                                width="700"
                                trigger="hover"
                                @show="showZentaoTaskList(scope.row)"
                                @hide="hideZentaoTaskList"
                            >
                                <ZentaoTaskList
                                    :show="isShowZentaoTaskList"
                                    :proProjectId="scope.row.proProjectId"
                                    :proTaskId="scope.row.proTaskId"
                                    :projectManagerAccount="
                                        currentRowProjectManager
                                    "
                                ></ZentaoTaskList>
                                <svg-icon
                                    slot="reference"
                                    icon-class="maintenanceProject-creat-task"
                                    class="task-icon blue"
                                    @click="handleCreateTask(scope.row)"
                                />
                            </el-popover>
                            <el-tooltip
                                v-if="!scope.row.proTaskId"
                                effect="dark"
                                content="无关联禅道任务"
                                placement="top"
                            >
                                <svg-icon
                                    icon-class="maintenanceProject-creat-task"
                                    class="task-icon gray"
                                    @click="handleCreateTask(scope.row)"
                                />
                            </el-tooltip>
                            <!-- 编辑风险 -->
                            <svg-icon
                                v-permission="['maintenanceProjectRiskUpdate']"
                                icon-class="maintenanceProject-edit"
                                class="edit-icon"
                                @click="handleCreateRisk(scope.row)"
                            />
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                class="pagination"
                :total="total"
                :page.sync="page"
                :limit.sync="limit"
                @pagination="queryParamsSelect"
            />
        </div>
        <!-- 建立禅道任务弹窗 -->
        <TargetZentaoTaskDialog
            :visible.sync="targetZentaoTaskDialogVisible"
            :projectManager="currentRowProjectManager"
            @save="createZentaoTask"
            :isOrderOrRisk="projectType === 'develop'"
        ></TargetZentaoTaskDialog>
        <RiskUpdateDialog
            :type="type"
            :id="id"
            :visible.sync="riskUpdateDialogVisible"
            @success="queryParamsSelect"
            :projectType="projectType"
            :assProjectName="projectName"
            :assProjectId="projectId"
            :proProjectId="proProjectId"
        ></RiskUpdateDialog>
    </div>
</template>

<script>
import CollapsibleSearchPanel from 'Components/CollapsibleSearchPanel.vue';
import TargetZentaoTaskDialog from 'maintenanceProject/components/TargetZentaoTaskDialog/index.vue';
import ZentaoTaskList from 'maintenanceProject/components/ZentaoTaskList';
import RiskUpdateDialog from 'maintenanceProject/components/RiskUpdateDialog';
import { CONSTANTS } from '@/constants';
import { queryParams, queryConfig, navItems } from './formInit.js';

const oriQureryParams = {
    faultPlanType: '',
    riskType: '',
    riskLevel: '',
    riskTitle: '',
    productModel: '',
    riskPlanType: '',
    hasNoCloseSupportType: ''
};
export default {
    name: 'RiskList',
    components: {
        CollapsibleSearchPanel,
        TargetZentaoTaskDialog,
        RiskUpdateDialog,
        ZentaoTaskList
    },
    props: {
        // 当前页签
        activeName: {
            type: String,
            default: ''
        },
        productLine: {
            type: String,
            default: ''
        },
        subProductLine: {
            type: String,
            default: ''
        },
        projectManager: {
            type: String,
            default: ''
        },
        // 项目ID(这时为开发项目)
        projectId: {
            type: String,
            default: ''
        },
        // 当前开发项目的名称
        projectName: {
            type: String,
            default: ''
        },
        // 当前开发项目对应的禅道项目
        proProjectId: {
            type: [String, Number],
            default: ''
        },
        // 维护项目/开发项目
        projectType: {
            type: String,
            default: 'maintenance'
        },
        // 项目状态
        projectStatus: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            total: 0,
            page: 1,
            limit: 50,
            type: 'add',
            tableData: [],
            // 默认查询条件
            navActiveName: '未关闭的',
            navItems,
            defaultField: 'faultStatus',
            // 顶部级联组件key
            projectSelectorKey: 0,
            // 建立禅道任务弹窗
            targetZentaoTaskDialogVisible: false,
            // 编辑风险弹窗
            riskUpdateDialogVisible: false,
            // 新建风险按钮权限（这里的权限和建立风险的禅道任务是一起的）
            showRiskPermission: this.$store.state.permission.btnDatas.includes(
                'maintenanceProjectRiskUpdate'
            ),
            queryParams,
            queryConfig,
            CONSTANTS,
            rowData: {},
            // 风险ID
            id: '',
            // 当前行的项目经理
            currentRowProjectManager: '',
            // 是否展示禅道任务列表
            isShowZentaoTaskList: false,
            // 是否正在查询
            isQuerying: false
        };
    },
    computed: {
        isDot() {
            const { currentPage, pageSize, ...restParams } = this.queryParams;
            return Object.entries(restParams).some(
                ([_, value]) => value !== ''
            );
        },
        // 产品名称下拉选项
        productModelOptions() {
            const res = [];
            this.tableData.forEach((i) => {
                if (i.productModel) {
                    res.push(i.productModel);
                }
            });
            return [...new Set(res)].map((i) => ({
                value: i,
                label: i
            }));
        },
        createTaskByStatusDisabled() {
            if (this.projectType === 'develop') {
                const list = ['已结项', '已终止', '市场待立项', '排队中'];
                return list.includes(this.projectStatus);
            }
            return false;
        }
    },
    watch: {
        productLine(newVal) {
            if (this.activeName === 'riskList') {
                this.queryParamsSelect();
            }
        },
        subProductLine(newVal) {
            if (this.activeName === 'riskList') {
                this.queryParamsSelect();
            }
        },
        projectManager(newVal) {
            if (this.activeName === 'riskList') {
                this.queryParamsSelect();
            }
        },
        projectId(newVal) {
            if (this.activeName === 'riskList') {
                this.queryParamsSelect();
            }
        }
    },
    created() {
        this.getRiskTypeOptions();
    },
    activated() {
        // 保证这里的查询在projectId等变更之后
        setTimeout(() => {
            this.queryParamsSelect();
        }, 0);
    },
    methods: {
        /**
         * 创建禅道任务
         * @param {string} value - 项目ID
         */
        async createZentaoTask(value) {
            const api = this.$service.maintenanceProject.risk.createZentaoTask;
            const params = {
                ...this.rowData,
                proProjectId: value
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.$message.success('创建成功');
                this.targetZentaoTaskDialogVisible = false;
                this.queryParamsSelect();
            } catch (error) {
                console.error(error, 'error');
            }
        },
        /**
         * 执行查询操作
         * @param {Object} form - 查询表单参数，默认使用this.queryParams
         */
        async handleQuery(form = this.queryParams) {
            if (this.projectType === 'maintenance' && !this.productLine) {
                return;
            } else if (this.projectType === 'develop' && !this.projectId) {
                return;
            }
            if (this.isQuerying) return;
            this.isQuerying = true;
            let params = this.$tools.cloneDeep(form);

            params = {
                ...params,
                productLine: this.productLine,
                subProductLine: this.subProductLine,
                projectManager: this.projectManager,
                assProjectId: this.projectId,
                // 流程软硬件归属，固定为硬件
                flowSoftHardBelong: '硬件',
                riskStageType: this.projectType === 'develop' ? '开发' : '维护',
                pageSize: this.limit,
                currentPage: this.page
            };
            try {
                const api = this.$service.maintenanceProject.risk.getList;
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                const { startRow, total } = res.body;
                this.total = total;
                this.tableData = res.body.list.map((item, index) => {
                    if (startRow) {
                        item.index = startRow + index;
                    }
                    return item;
                });
                this.queryConfig.items.find(
                    (i) => i.name === '产品型号'
                ).elOptions = this.productModelOptions;
            } catch (error) {
                console.error(error, 'error');
            } finally {
                this.isQuerying = false;
            }
        },
        /**
         * 根据当前选中的导航项执行查询
         */
        queryParamsSelect() {
            if (this.navActiveName) {
                const item = this.navItems.find(
                    (i) => i.name === this.navActiveName
                );
                this.handleNavChange(item);
            } else {
                this.handleQuery();
            }
        },
        /**
         * 处理搜索按钮点击
         */
        handleSearch() {
            this.navActiveName = '';
            this.handleQuery();
        },
        /**
         * 处理重置按钮点击
         */
        handleReset() {
            this.navActiveName = '';
            this.queryParams = this.$tools.cloneDeep(oriQureryParams);
            this.handleQuery();
        },
        addRisk() {
            this.type = 'add';
            this.id = '';
            this.riskUpdateDialogVisible = true;
        },
        /**
         * 跳转到详情页
         * @param  {Object} row 行数据
         */
        handleRoutingJump(row) {
            this.$router.push({
                path:
                    this.projectType === 'develop'
                        ? 'projectRiskDetail'
                        : 'maintenanceRiskDetail',
                query: {
                    risk_id: row.riskId
                }
            });
        },
        /**
         * 建立禅道任务
         * @param  {Object} row 行数据
         */
        handleCreateTask(row) {
            if (row.proTaskId || !this.showRiskPermission) return;
            this.rowData = row;
            this.currentRowProjectManager = row.projectManager;
            this.targetZentaoTaskDialogVisible = true;
        },
        /**
         * 编辑风险
         * @param  {Object} row 行数据
         */
        handleCreateRisk(row) {
            this.id = row.riskId;
            this.type = 'edit';
            this.riskUpdateDialogVisible = true;
        },
        /**
         * 处理导航切换
         * @param {Object} item - 导航项数据
         */
        handleNavChange(item) {
            const form = this.$tools.cloneDeep(oriQureryParams);
            const { queryField, field } = item;
            form[queryField] = field;
            this.handleQuery(form);
        },
        /**
         * 获取风险类型对应的选项
         */
        async getRiskTypeOptions() {
            const api = this.$service.project.finance.getSelectOptions;
            const params = { paramName: '风险类型', paramType: '' };
            const res = await api(params);
            if (res.head.code !== '000000') {
                this.$message.error(res.head.message);
                return;
            }
            this.queryConfig.items.find(
                (i) => i.name === '风险类型'
            ).elOptions = res.body.map((i) => ({
                value: i.paramName,
                label: i.paramName
            }));
        },
        /**
         * 显示禅道任务列表
         * @param {Object} row - 行数据
         */
        showZentaoTaskList(row) {
            this.proProjectId = row.proProjectId;
            this.currentRowProjectManager = row.projectManager;
            this.isShowZentaoTaskList = true;
        },
        /**
         * 隐藏禅道任务列表
         */
        hideZentaoTaskList() {
            this.isShowZentaoTaskList = false;
        }
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
.flex-center {
    align-items: center;
    justify-content: center;
}
.gap-5 {
    gap: 5px;
}
.mt-5 {
    // margin-top: 5px;
    padding-top: 5px;
}
.gray {
    color: #bfbfbf;
}
.blue {
    color: #18a8f8;
}
.risk-icon {
    width: 22px;
    height: 22px;
    &:hover {
        cursor: pointer;
        transform: scale(1.2);
    }
}
.task-icon {
    width: 22px;
    height: 22px;
    &:hover {
        cursor: pointer;
        transform: scale(1.2);
    }
}
.edit-icon {
    width: 22px;
    height: 22px;
    color: #18a8f8;
    &:hover {
        cursor: pointer;
        transform: scale(1.2);
    }
}
.overview-ellipsis {
    color: #3370ff;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    line-clamp: 2;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-align: left;
}
.content-area {
    margin-top: 10px;
    .pagination {
        padding: 0px;
        margin-top: 10px;
    }
}
::v-deep .el-table .el-table__row {
    height: auto !important;
}
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border-right: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border-bottom: 1px solid #dfe6ec !important;
}
</style>
<style>
.maintananceProject-tooltip-width.el-tooltip__popper {
    max-width: 500px;
}
</style>
