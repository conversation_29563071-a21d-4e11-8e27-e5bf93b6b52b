<template>
    <el-table
        class="my-meetings-query"
        :data="tableList"
        :header-cell-style="{
            'text-align': 'center'
        }"
        :row-style="{ height: '35px!important' }"
        empty-text="无会议数据"
    >
        <el-table-column align="left" prop="meetingTitle" label="会议名称">
            <template slot-scope="scope">
                <el-tooltip
                    class="item"
                    effect="dark"
                    :content="scope.row.meetingTitle"
                    placement="top"
                >
                    <el-button
                        class="ellipsis-text"
                        type="text"
                        @click="handleMeetingClick(scope.row)"
                    >
                        {{ scope.row.meetingTitle }}
                    </el-button>
                </el-tooltip>
            </template>
        </el-table-column>
        <el-table-column
            align="center"
            label="类型"
            width="100"
            prop="meetingType"
        >
        </el-table-column>
        <el-table-column
            prop="myRole"
            label="我的角色"
            align="center"
            width="120"
        >
        </el-table-column>
        <el-table-column
            prop="organizerName"
            label="会议组织者"
            align="center"
            width="80"
        >
        </el-table-column>
        <el-table-column
            v-for="(i, index) in daysOfWeek"
            :label="i"
            :key="i"
            align="center"
            width="80"
        >
            <el-table-column
                :key="weekList[index]"
                :label="weekList[index]"
                align="center"
                width="80"
            >
                <template slot-scope="scope">
                    {{ scope.row[daysOfWeek[index]] }}
                </template>
            </el-table-column>
        </el-table-column>
        <el-table-column prop="其他" label="其他" align="center" width="80">
        </el-table-column>
    </el-table>
</template>

<script>
const weekList = [
    '星期一',
    '星期二',
    '星期三',
    '星期四',
    '星期五',
    '星期六',
    '星期日'
];

export default {
    name: 'MyMeetingsQueryList',
    props: {
        tableList: {
            type: Array,
            default: () => []
        },
        daysOfWeek: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            // 日期数组
            dateArr: [],
            weekList
        };
    },
    methods: {
        handleMeetingClick(row) {
            this.$router.push({
                path: 'meetingDetail',
                query: { id: row.meetingId }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.ellipsis-text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: left;
    padding: 0px !important;
    max-width: 100%;
    height: 23px;
}
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border: 1px solid #8c8c8c !important;
}
.my-meetings-query {
    border: 1px solid #8c8c8c !important;
}
</style>
