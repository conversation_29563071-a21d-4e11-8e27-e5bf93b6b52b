<template>
    <div>
        <el-dialog
            title="编辑订单"
            :visible.sync="dialogVisible"
            :before-close="reset"
            width="70%"
        >
            <!-- 基本信息 -->
            <el-divider>基本信息</el-divider>
            <el-descriptions :column="4" border>
                <el-descriptions-item label="客户名称" :span="2">{{
                    form.customerName
                }}</el-descriptions-item>
                <el-descriptions-item label="项目经理" :span="2">{{
                    form.projectManagerName
                }}</el-descriptions-item>
                <el-descriptions-item label="OA流程状态">{{
                    form.flowStatus
                }}</el-descriptions-item>
                <el-descriptions-item label="OA流程">{{
                    form.flowNo
                }}</el-descriptions-item>
                <el-descriptions-item label="产品型号">{{
                    form.productModel
                }}</el-descriptions-item>
                <el-descriptions-item label="产品名称">{{
                    form.productName
                }}</el-descriptions-item>
                <el-descriptions-item label="产品线">{{
                    form.productLine
                }}</el-descriptions-item>
                <el-descriptions-item label="细分产品线">{{
                    form.subProductLine
                }}</el-descriptions-item>
            </el-descriptions>

            <el-divider>订单识别</el-divider>
            <el-descriptions :column="5" border class="mt-20">
                <el-descriptions-item label="生产单位">{{
                    form.productUnit
                }}</el-descriptions-item>
            </el-descriptions>
            <el-table
                :data="form.orderRequireList"
                border
                class="base-config-table"
            >
                <el-table-column
                    header-align="center"
                    prop="baseConfigRequire"
                    label="基本配置要求"
                ></el-table-column>
                <el-table-column
                    align="center"
                    header-align="center"
                    prop="orderCount"
                    label="数量"
                    width="80"
                ></el-table-column>
                <el-table-column
                    align="center"
                    header-align="center"
                    prop="requireDate"
                    label="要求供货日期"
                    width="150"
                ></el-table-column>
            </el-table>
            <el-divider>订单应对计划</el-divider>

            <el-form
                ref="form"
                :model="form"
                class="form"
                size="small"
                label-width="120px"
            >
                <el-form-item
                    label="订单应对计划"
                    prop="orderPlanType"
                    :rules="required"
                >
                    <el-radio-group
                        v-model="form.orderPlanType"
                        :disabled="orderPlanTypeDisabled"
                    >
                        <el-radio label="不需要创建">不需要创建</el-radio>
                        <el-radio label="需要创建">需要创建</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item
                    label="关联禅道项目"
                    prop="proProjectId"
                    v-show="form.orderPlanType === '需要创建'"
                    :rules="form.orderPlanType === '需要创建' ? required : {}"
                >
                    <el-select
                        class="w-100"
                        v-model="form.proProjectId"
                        placeholder="请选择关联禅道项目"
                        :disabled="orderPlanTypeDisabled"
                        @change="getZentaoTaskList"
                        filterable
                    >
                        <el-option
                            v-for="item in zentaoProjectList"
                            :label="item.name"
                            :key="item.id"
                            :value="item.id"
                        ></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item
                    label="关联禅道任务"
                    prop="proTaskId"
                    v-if="form.orderPlanType === '需要创建' && form.proTaskId"
                >
                    <div class="flex space-between">
                        <el-select
                            v-if="form.newProTaskId && form.newProTaskId !== 0"
                            class="w-100"
                            v-model="form.newProTaskId"
                            disabled
                            filterable
                        >
                            <el-option
                                v-for="item in zentaoTaskList"
                                :label="item.name"
                                :key="item.id"
                                :value="item.id"
                            ></el-option>
                        </el-select>
                        <div v-else-if="form.newProTaskId === 0"></div>
                        <el-select
                            v-else-if="form.proTaskId"
                            class="w-100"
                            v-model="form.proTaskId"
                            disabled
                            filterable
                        >
                            <el-option
                                v-for="item in zentaoTaskList"
                                :label="item.name"
                                :key="item.id"
                                :value="item.id"
                            ></el-option>
                        </el-select>
                        <div v-else></div>
                        <ChangeTaskDialog
                            v-if="form.proProjectId"
                            :hasTaskHour="consumed !== '0.0'"
                            :zentaoProjectList="zentaoProjectList"
                            @save="handleChangeTaskSave"
                            class="pl-20"
                        ></ChangeTaskDialog>
                    </div>
                </el-form-item>
            </el-form>
            <div class="footer" slot="footer">
                <el-button @click="closeDialog">取 消</el-button>
                <el-button type="primary" @click="save">保 存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants';
import { getUserAccount } from 'feature/views/meetingManagement/commonFunction';
import ChangeTaskDialog from 'maintenanceProject/components/ChangeTaskDialog/index.vue';

export default {
    name: 'OrderUpdateDialog',
    components: { ChangeTaskDialog },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            default: 'add'
        },
        id: {
            type: String,
            default: ''
        },
        // 项目类型（开发/维护）
        projectType: {
            type: String,
            default: '维护'
        }
    },
    data() {
        return {
            form: {
                defectTitle: '',
                status: '',
                projectManager: '',
                source: '',
                oaStatus: '',
                processId: '',
                model: '',
                productName: '',
                productLine: '',
                subProductLine: '',
                discoveryTime: '',
                severityLevel: '',
                problemDefinition: '',
                description: '',
                defectList: [],
                orderPlanType: '',
                proProjectId: '',
                proTaskId: '',
                zentaoProject: '',
                zentaoTask: '',
                rootCause: '',
                solution: '',
                raisedToCommittee: false,
                resolver: ''
            },
            CONSTANTS,
            required: {
                required: true,
                // 注意这里必填提示是一个空额，为了避免和输入框等位置冲突
                message: ' ',
                trigger: ['change', 'blur']
            },
            zentaoProjectList: [],
            zentaoTaskList: [],
            consumed: '0.0',
            orderPlanTypeDisabled: false
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                // 编辑的时候先查详情，后根据详情的项目经理获取禅道项目
                if (this.id) {
                    this.getDetail();
                } else {
                    this.getZentaoProjectList();
                }
            }
        }
    },
    mounted() {
        this.getZentaoProjectList();
    },
    methods: {
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.$refs.form.resetFields();
            this.dialogVisible = false;
            this.$emit('update');
        },
        /**
         * 保存
         */
        async save() {
            const valid = await this.validForm();
            if (!valid) {
                this.$message.warning('请输入所有必填项');
                return;
            }
            // 编辑的时候，如果此时没有创建任务，并且选择了需要创建
            // 将 newProTaskId 改为 0
            if (
                !this.form.proTaskId &&
                this.form.orderPlanType === '需要创建'
            ) {
                this.form.newProTaskId = 0;
            }
            if (this.form.orderPlanType === '不需要创建') {
                this.form.proProjectId = '';
            }
            const api = this.$service.maintenanceProject.order.updateOrder;
            try {
                const res = await api({
                    ...this.form,
                    orderId: this.id,
                    orderStageType: this.projectType,
                    // 流程软硬件归属，固定为硬件
                    flowSoftHardBelong: '硬件'
                });
                if (res.head.code === '000000') {
                    this.$message.success('保存成功');
                    this.$emit('success');
                    this.closeDialog();
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 校验
         */
        async validForm() {
            try {
                await this.$refs.form.validate();
                return true;
            } catch (error) {
                return false;
            }
        },
        /**
         * 关闭弹窗前的回调
         * @param {Function} done 关闭弹窗的函数
         */
        reset(done) {
            this.$refs.form.resetFields();
            done();
        },
        /**
         * 获取禅道项目下拉列表
         */
        async getZentaoProjectList() {
            const api =
                this.$service.maintenanceProject.zentao.getZentaoProjectList;
            const params = {
                projectManagerAccount: this.id
                    ? this.form.projectManager
                    : getUserAccount()
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.zentaoProjectList = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 根据项目获取子任务接口
         */
        async getZentaoTaskList() {
            if (!this.form.proProjectId) return;
            const api =
                this.$service.maintenanceProject.zentao.getZentaoTaskList;
            try {
                const res = await api({ proProjectId: this.form.proProjectId });
                if (res.head.code === '000000') {
                    this.zentaoTaskList = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 获取订单详情
         */
        async getDetail() {
            const api = this.$service.maintenanceProject.order.getDetail;
            const params = {
                orderId: this.id,
                // 流程软硬件归属，固定为硬件
                flowSoftHardBelong: '硬件'
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.form = res.body;
                    // 如果已经关联了禅道项目，就禁用掉
                    this.orderPlanTypeDisabled =
                        this.form.orderPlanType === '需要创建';
                    this.form.proProjectId && this.getTaskList();
                    this.getZentaoProjectList();
                    this.getZentaoTaskList();
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 处理更改任务
         */
        handleChangeTaskSave(value) {
            this.form.proProjectId = value.proProjectId;
            this.form.newProTaskId = value.proTaskId;
            this.getZentaoTaskList();
        },
        /**
         * 获取禅道任务列表
         */
        async getTaskList() {
            const api =
                this.$service.maintenanceProject.zentao
                    .getZentaoTaskListInProject;
            const params = {
                id: this.form.proTaskId,
                proProjectId: this.form.proProjectId
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                // 获取该任务消耗工时
                if (res.body.length > 0) {
                    const { consumed = null } = res.body[0];
                    this.consumed = consumed;
                } else {
                    this.consumed = '0.0';
                }
            } catch (error) {
                console.error(error, 'error');
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.space-between {
    justify-content: space-between;
}
.ml-10 {
    margin-left: 10px;
}
.w-50 {
    width: 50%;
}
.w-100 {
    width: 100%;
}
.minutes {
    margin-top: 15px;
    justify-content: flex-start;
}
.footer {
    display: flex;
    justify-content: center;
}
.pl-20 {
    padding-left: 20px;
}
.base-config-table {
    margin-top: 20px;
    border: 1px solid #8c8c8c !important;
}
::v-deep .form .el-form-item__label {
    font-weight: bold;
}
.pre-wrap-text {
    white-space: pre-wrap;
}
::v-deep .el-table .el-table__row {
    height: auto !important;
}
::v-deep .el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border-right: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border: 1px solid #8c8c8c !important;
}
</style>
