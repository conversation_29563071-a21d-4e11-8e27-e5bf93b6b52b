<template>
    <div>
        <el-empty
            :image-size="50"
            v-if="this.chartData.length === 0"
        ></el-empty>
        <OrgChart
            v-else
            :datasource.sync="dataSource"
            class="project-org-chart"
        >
            <template v-slot:prefix
                ><div class="project-org-legend">
                    <div class="info">
                        <i class="fa fa-solid fa-user manager"></i>项目经理
                    </div>
                    <div class="info">
                        <i class="fa fa-solid fa-user pqa"></i>PQA/开发代表
                    </div>
                    <div class="info">
                        <i class="fa fa-solid fa-user engineer"></i>工程师
                    </div>
                </div></template
            >
        </OrgChart>
    </div>
</template>
<script>
import OrgChart from 'Components/OrgChart/OrgChart.vue';

export default {
    name: 'ProjectOrgChart',
    components: { OrgChart },
    props: {
        chartData: {
            type: Array,
            default: () => []
        }
    },
    watch: {
        chartData(newVal) {
            if (newVal.length > 0) {
                this.handleData(newVal);
            }
        }
    },
    data() {
        return {
            dataSource: {}
        };
    },
    methods: {
        /**
         * 处理数据，将其变成树状结构
         * @param {Array} data 接口数据
         */
        handleData(data) {
            // 第一次遍历处理开发代表与工程师的关系，整理数据
            const developers = data
                .slice(1)
                .flatMap((i) => {
                    if (i.leader.length === 0 && i.personnel.length === 0) {
                        return { id: 'invalidData' };
                    }
                    // 每个模块一个纵排，将开发代表和工程师都排成一个纵排
                    const createNestedObject = (leaders, i) => {
                        if (leaders.length === 0) return {};
                        const numbers = i.leader.length + i.personnel.length;
                        // 第一级为各个模块
                        const moduleObject = {
                            id: i.position,
                            name: `${i.position}(${numbers})`,
                            title: i.position,
                            backgroundColor: '#3370ff',
                            color: '#fff',
                            borderColor: '#3297e4',
                            showIcon: false,
                            children: []
                        };

                        let rootObject = null;
                        let currentObject = null;
                        // 处理开发代表与PQA
                        for (let m = 0; m < leaders.length; m++) {
                            const leader = leaders[m];
                            const newObject = {
                                id: leader,
                                name: leader,
                                title: i.position,
                                backgroundColor: '#d4f4ff',
                                color: '#3297e4',
                                borderColor: '#3297e4',
                                iconColor: '#32CD32',
                                children: []
                            };

                            if (m === leaders.length - 1) {
                                // 开发代表的最后一个元素就是工程师
                                newObject.children =
                                    i.personnel.length > 0
                                        ? this.generateChildren(
                                              i.personnel,
                                              i.positionTitle
                                          )
                                        : [];
                            }

                            if (rootObject === null) {
                                rootObject = newObject;
                            } else {
                                currentObject.children = [newObject];
                            }

                            currentObject = newObject;
                        }
                        moduleObject.children = [rootObject];
                        return moduleObject;
                    };

                    let devs = createNestedObject(i.leader, i);
                    // 处理 leader 为空但 personnel 不为空的情况
                    if (i.leader.length === 0 && i.personnel.length !== 0) {
                        const numbers = i.personnel.length;
                        devs = {
                            id: i.position,
                            name: `${i.position}(${numbers})`,
                            title: i.position,
                            backgroundColor: '#3370ff',
                            color: '#fff',
                            borderColor: '#3297e4',
                            children:
                                i.personnel.length > 0
                                    ? this.generateChildren(
                                          i.personnel,
                                          i.positionTitle
                                      )
                                    : []
                        };
                    }

                    return devs;
                })
                .filter((dev) => dev.id !== 'invalidData');

            // 处理第一个数据项: 项目经理
            const res = {
                id: data[0].leader || data[0].position,
                name: data[0].leader.join('') || '暂无',
                title: data[0].position,
                backgroundColor: '#d4f4ff',
                color: '#3297e4',
                borderColor: '#3297e4',
                iconColor: '#4169E1',
                children: developers
            };

            this.dataSource = res;
        },
        /**
         * 将每个工程师的childen加到上一个工程师的children中
         * @param {Array} personnel 工程师数组
         * @param {String} positionTitle 职位
         */
        generateChildren(personnel, positionTitle) {
            const childrenList = personnel.map((k, index) => ({
                id: k,
                name: k,
                title: positionTitle,
                backgroundColor: '#d4f4ff',
                color: '#3297e4',
                borderColor: '#3297e4',
                iconColor: '#8b3e86'
            }));
            // 将 childrenList 转换为嵌套结构
            let res = [];
            for (let i = childrenList.length - 1; i >= 0; i--) {
                res = [
                    {
                        ...childrenList[i],
                        children: res
                    }
                ];
            }
            return res;
        }
    }
};
</script>
<style lang="scss" scoped>
.project-org-chart {
    padding-top: 20px;
    margin-top: 15px;
    height: calc(100vh - 170px);
    position: relative;
    .project-org-legend {
        position: absolute;
        top: 10px;
        left: 10px;
        display: flex;
        .info {
            margin-right: 15px;
        }
        .manager {
            margin-right: 5px;
            color: #4169e1;
        }
        .pqa {
            margin-right: 5px;
            color: #32cd32;
        }
        .engineer {
            margin-right: 5px;
            color: #8b3e86;
        }
    }
}
</style>
