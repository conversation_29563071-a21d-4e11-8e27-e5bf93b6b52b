<template>
    <!-- TODO：风险需要拆成一条条的 -->
    <div class="risk-container">
        <div class="risk-header">
            <div class="title">主要风险及需支持事项</div>
            <el-button type="text" @click="handleSelectRisk" v-if="editable"
                >选择风险</el-button
            >
        </div>
        <el-table
            :data="riskList"
            style="width: 100%"
            :span-method="objectSpanMethod"
        >
            <el-table-column
                prop="index"
                label="序号"
                width="60"
                align="center"
            />
            <el-table-column
                prop="riskDesc"
                label="描述及影响"
                min-width="180"
                header-align="center"
            />
            <el-table-column
                prop="solutionMeasures"
                label="解决措施"
                min-width="180"
                header-align="center"
            />
            <el-table-column
                prop="riskLevel"
                label="风险等级"
                width="80"
                align="center"
            >
            </el-table-column>
            <el-table-column
                prop="supportItem"
                label="需支持事项"
                min-width="150"
                header-align="center"
            />
            <el-table-column
                prop="expectedDate"
                label="期望达成日期"
                width="120"
                align="center"
            />
            <el-table-column
                prop="responsibleOrg"
                label="提供支持责任部门"
                min-width="150"
                header-align="center"
            >
                <template slot-scope="scope">
                    <el-tooltip
                        effect="dark"
                        :content="scope.row.responsiblePerson"
                        placement="top"
                        :disabled="!scope.row.responsiblePerson"
                    >
                        <span class="cursor-pointer">{{
                            scope.row.responsibleOrg
                        }}</span>
                    </el-tooltip>
                </template>
            </el-table-column>
            <el-table-column
                label="操作"
                width="80"
                align="center"
                v-if="editable"
            >
                <template slot-scope="scope">
                    <el-button
                        type="primary"
                        size="small"
                        @click="handleEdit(scope.row)"
                        >编辑</el-button
                    >
                </template>
            </el-table-column>
        </el-table>
        <RiskUpdateDialog
            type="edit"
            :id="riskId"
            :visible.sync="riskUpdateDialogVisible"
            @success="updateWeeklyRisk"
            projectType="develop"
            :assProjectName="projectName"
            :assProjectId="projectId"
            :proProjectId="proProjectId"
            :isWeekly="true"
        ></RiskUpdateDialog>
        <SelectListDialog
            :visible.sync="selectListDialogVisible"
            :list="selectRiskList"
            :weeklyId="weeklyId"
            @success="handleSelect"
        ></SelectListDialog>
    </div>
</template>

<script>
import RiskUpdateDialog from 'maintenanceProject/components/RiskUpdateDialog';
import SelectListDialog from './SelectListDialog';

export default {
    name: 'Risk',
    components: {
        RiskUpdateDialog,
        SelectListDialog
    },
    props: {
        editable: {
            type: Boolean,
            default: false
        },
        // 风险列表
        riskList: {
            type: Array,
            default: () => []
        },
        // 项目ID
        projectId: {
            type: [Number, String],
            default: ''
        },
        // 周报ID
        weeklyId: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            // 编辑风险弹窗是否可见的变量
            riskUpdateDialogVisible: false,
            // 风险ID
            riskId: '',
            // 选择风险弹窗是否可见的变量
            selectListDialogVisible: false,
            // 用于选择风险的下拉列表
            selectRiskList: [],
            // 选中的当前行
            currentRow: {}
        };
    },
    computed: {
        // 项目名称
        projectName() {
            return this.$store.state.project.projectInfo.projectName;
        },
        // 禅道项目ID
        proProjectId() {
            return this.$store.state.project.projectInfo.proProjectId;
        }
    },
    methods: {
        /**
         * 选择风险结束后
         */
        handleSelect() {
            this.$emit('selectSuccess');
        },
        /**
         * 编辑风险
         * @param {Object} row 行数据
         */
        handleEdit(row) {
            this.riskId = row.riskId;
            this.currentRow = row;
            this.riskUpdateDialogVisible = true;
        },
        /**
         * 点击“选择风险”按钮，弹出选择风险弹窗
         */
        handleSelectRisk() {
            this.getSelectRiskList();
            this.selectListDialogVisible = true;
        },
        /**
         * 获取风险选择列表
         */
        async getSelectRiskList() {
            const api = this.$service.project.weekly.getSelectRiskList;
            const params = {
                projectId: this.projectId,
                weekReportId: this.weeklyId
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.selectRiskList = res.body;
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 风险编辑成功
         */
        async updateWeeklyRisk() {
            const api = this.$service.project.weekly.updateWeeklyRisk;
            const { riskId, weekReportId } = this.currentRow;
            const params = {
                riskId,
                weekReportId,
                riskWeekReportId: this.currentRow.id
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.$emit('refresh');
            } catch (error) {
                console.error('Error:', error);
            }
        },
        /**
         * 合并单元格
         * @param {Object} param 表格数据
         * @returns {Object} 合并单元格
         */
        objectSpanMethod({ row, column, rowIndex, columnIndex }) {
            // 只处理序号，描述及影响、解决措施、风险等级的合并
            if (![0, 1, 2, 3].includes(columnIndex)) {
                return {
                    rowspan: 1,
                    colspan: 1
                };
            }

            // 根据列确定要比较的字段
            const getCompareField = (colIndex) => {
                switch (colIndex) {
                    case 0:
                        return 'index';
                    case 1:
                        return 'riskDesc';
                    case 2:
                        return 'solutionMeasures';
                    case 3:
                        return 'riskLevel';
                    default:
                        return '';
                }
            };

            const compareField = getCompareField(columnIndex);
            if (rowIndex === 0) {
                let count = 1;
                for (let i = 1; i < this.riskList.length; i++) {
                    if (
                        this.riskList[i][compareField] === row[compareField] &&
                        this.riskList[i].riskId === row.riskId
                    ) {
                        count += 1;
                    } else {
                        break;
                    }
                }
                return {
                    rowspan: count,
                    colspan: 1
                };
            }

            // 与上一行比较，判断是否需要合并
            const prevRow = this.riskList[rowIndex - 1];
            // 必须是相同会议才会进行合并
            if (
                prevRow[compareField] === row[compareField] &&
                prevRow.riskId === row.riskId
            ) {
                return {
                    rowspan: 0,
                    colspan: 0
                };
            }

            // 计算当前行需要合并的行数
            let count = 1;
            for (let i = rowIndex + 1; i < this.riskList.length; i++) {
                if (
                    this.riskList[i][compareField] === row[compareField] &&
                    this.riskList[i].riskId === row.riskId
                ) {
                    count += 1;
                } else {
                    break;
                }
            }

            return {
                rowspan: count,
                colspan: 1
            };
        }
    }
};
</script>

<style lang="scss" scoped>
@import 'project/views/projectReport/components/common/common.scss';

.risk-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    border-bottom: 1px solid #8c8c8c;
    height: 28px;
    .title {
        @include section-title;
    }
}

.cursor-pointer {
    cursor: pointer;
}
</style>
