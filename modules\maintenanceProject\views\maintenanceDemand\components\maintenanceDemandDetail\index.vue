<template>
    <div class="maintenance-defect-detail">
        <div class="flex">
            <h3>需求详情</h3>
            <div>
                <el-button
                    v-permission="['maintenanceProjectDemandUpdate']"
                    type="primary"
                    @click="handleEdit"
                    >编辑</el-button
                >
                <el-button type="primary" @click="goBack">返回</el-button>
            </div>
        </div>

        <!-- 基本信息 -->
        <el-divider>基本信息</el-divider>
        <el-descriptions :column="4" border>
            <el-descriptions-item label="需求名称" :span="4">
                {{ formData.demandName }}
            </el-descriptions-item>
            <el-descriptions-item label="客户名称" :span="2">
                {{ formData.customerName }}
            </el-descriptions-item>
            <el-descriptions-item label="项目经理">
                {{ formData.projectManagerName }}
            </el-descriptions-item>
            <el-descriptions-item label="需求阶段">
                {{ formData.demandStage }}
            </el-descriptions-item>
            <el-descriptions-item label="OA流程状态">
                {{ formData.flowStatus }}
            </el-descriptions-item>
            <el-descriptions-item label="OA流程ID" :span="2">
                {{ formData.flowNo }}
            </el-descriptions-item>
            <el-descriptions-item label="产品型号">
                {{ formData.productModel }}
            </el-descriptions-item>
            <el-descriptions-item label="产品名称">
                {{ formData.productName }}
            </el-descriptions-item>
            <el-descriptions-item label="产品线">
                {{ formData.productLine }}
            </el-descriptions-item>
            <el-descriptions-item label="细分产品线">
                {{ formData.subProductLine }}
            </el-descriptions-item>
        </el-descriptions>

        <el-divider>需求识别</el-divider>
        <el-descriptions :column="4" border class="mt-20">
            <el-descriptions-item label="需求接收时间">
                {{ formData.demandReceiveDate }}
            </el-descriptions-item>
            <el-descriptions-item label="优先级别">
                {{ formData.priorityLevel }}
            </el-descriptions-item>
            <el-descriptions-item label="需求要求满足时间" :span="2">
                {{ formData.dateRequest }}
            </el-descriptions-item>
            <el-descriptions-item label="需求背景" :span="4">
                <span class="pre-wrap-text">{{
                    formData.demandBackground
                }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="需求要点" :span="4">
                <span class="pre-wrap-text">{{
                    formData.demandKeyRequire
                }}</span>
            </el-descriptions-item>
            <el-descriptions-item label="需求规模" :span="4">
                {{ formData.demandScale }}
            </el-descriptions-item>
            <el-descriptions-item label="是否关联批量订单">
                {{ formData.whetherAssBatchOrder }}
            </el-descriptions-item>
        </el-descriptions>

        <template v-if="formData.proProjectId">
            <el-divider>需求应对</el-divider>
            <ZentaoTaskList
                class="mt-10"
                :show="isShowZentaoTaskList"
                :proProjectId="formData.proProjectId"
                :proTaskId="formData.proTaskId"
                :projectManagerAccount="projectManagerAccount"
            ></ZentaoTaskList>
        </template>

        <div v-show="formData.hasRisk === '是'">
            <el-divider>影响缺陷解决的风险</el-divider>
            <RiskList
                class="mt-10"
                :riskListCounter="riskListCounter"
                :assObjectId="id"
                relatedObject="维护项目需求"
            ></RiskList>
        </div>

        <el-divider>需求响应结果</el-divider>
        <el-descriptions :column="4" border class="mt-20">
            <el-descriptions-item label="接收研发反馈时间">
                {{ formData.developFeedbackDate }}
            </el-descriptions-item>
            <el-descriptions-item label="需求确认结果">
                {{ formData.demandConfirmResult }}
            </el-descriptions-item>
        </el-descriptions>

        <DemandUpdateDialog
            :visible.sync="demandUpdateDialogVisible"
            :id="id"
            @success="getDetail"
        ></DemandUpdateDialog>
    </div>
</template>

<script>
import ZentaoTaskList from 'maintenanceProject/components/ZentaoTaskList';
import RiskList from 'maintenanceProject/components/RiskList';
import DemandUpdateDialog from '../DemandList/DemandUpdateDialog';

export default {
    name: 'MaintenanceDemandDetail',
    components: {
        ZentaoTaskList,
        RiskList,
        DemandUpdateDialog
    },
    data() {
        return {
            formData: {
                demandName: '',
                customerName: '',
                projectManager: '',
                demandStage: '',
                flowStatus: '',
                flowNo: '',
                productModel: '',
                productName: '',
                productLine: '',
                subProductLine: '',
                demandReceiveDate: '',
                priorityLevel: '',
                developFeedbackDate: '',
                demandBackground: '',
                demandKeyRequire: '',
                demandScale: '',
                whetherAssBatchOrder: '',
                demandConfirmResult: '',
                demandPlanType: ''
            },
            demandUpdateDialogVisible: false,
            id: '',
            isShowZentaoTaskList: false,
            projectManagerAccount: '',
            proProjectId: '',
            riskListCounter: 0
        };
    },
    created() {
        const { query = null } = this.$route;

        if (query) {
            this.id = query.demand_id;
            this.getDetail();
        }
        this.closeAllPopovers();
    },
    methods: {
        goBack() {
            this.$router.go(-1);
        },
        handleEdit() {
            this.demandUpdateDialogVisible = true;
        },
        /**
         * 获取需求详情
         */
        async getDetail() {
            if (!this.id) return;
            const api = this.$service.maintenanceProject.demand.getDetail;
            const params = {
                id: this.id,
                // 流程软硬件归属，固定为硬件
                flowSoftHardBelong: '硬件'
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.formData = res.body;
                    this.projectManagerAccount = this.formData.projectManager;
                    this.proProjectId = this.formData.proProjectId;
                    this.isShowZentaoTaskList = true;
                    if (this.formData.hasRisk === '是') {
                        this.riskListCounter += 1;
                    }
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 关闭所有弹窗
         */
        closeAllPopovers() {
            this.$root.$emit('close-popovers');

            const popovers = document.querySelectorAll('.el-popover');
            popovers.forEach((popover) => {
                if (popover.style.display !== 'none') {
                    document.body.click();
                    popover.style.display = 'none';
                }
            });

            const tooltipPopovers = document.querySelectorAll(
                '.el-tooltip__popper'
            );
            tooltipPopovers.forEach((tooltipPopover) => {
                if (tooltipPopover.style.display !== 'none') {
                    document.body.click();
                    tooltipPopover.style.display = 'none';
                }
            });
        }
    }
};
</script>

<style lang="scss" scoped>
.maintenance-defect-detail {
    padding: 20px;
    height: calc(100vh - 20px);
    overflow: auto;
}
.flex {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.mt-10 {
    margin-top: 10px;
}
.mt-20 {
    margin-top: 20px;
}
.pre-wrap-text {
    white-space: pre-wrap;
}
::v-deep .el-descriptions-item__label {
    width: 120px;
}
</style>
