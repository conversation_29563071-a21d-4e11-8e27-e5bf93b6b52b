<template>
    <div class="milestone-container">
        <el-empty
            description="暂无数据"
            :image-size="30"
            v-if="wholeList.length === 0"
        ></el-empty>
        <div class="milestone-color-intro" v-else>
            <div class="flex">
                <svg-icon
                    icon-class="milestone-flag"
                    style="width: 20px; height: 20px; fill: red"
                ></svg-icon>
                <span>变更的里程碑</span>
            </div>
            <div class="flex" style="margin-left: 30px">
                <svg-icon
                    icon-class="project-clock"
                    class="milestone-flag"
                    style="fill: #00a745"
                />
                <span>正常进行</span>
                <svg-icon
                    icon-class="project-clock"
                    class="milestone-flag"
                    style="fill: #e59500"
                />
                <span>有延期风险</span>
                <svg-icon
                    icon-class="project-clock"
                    class="milestone-flag"
                    style="fill: red"
                />
                <span>已延期</span>
                <svg-icon
                    icon-class="project-check-mark"
                    class="check-mark-flag"
                />
                <span>已完成</span>
            </div>
        </div>
        <div v-for="(i, index) in wholeList">
            <Milestone
                :maxWidth="wholeWidth[0]"
                :milestoneData="i"
                :isCurrent="index === 0"
                :isLast="index === wholeList.length - 1"
                :wholeWidth="wholeWidth[index]"
                :progressWidth="progressWidth[index]"
                :version="wholeList[index].projectDetailVersion"
            ></Milestone>
        </div>
    </div>
</template>
<script>
/**
 * 里程碑组件(基线进度页面)
 * 每个里程碑都有自己的进度条，当里程碑延期时，会多一个虚线框，
 * 例如v2相对v1来说，延期了10天，那么v2的总体长度会在v1的基础上加上10天的虚线框，
 * 同时v2的进度条的长度是v1的进度条的长度加上v1虚线框的长度
 * 只有v0没有虚线框，v0的进度条长度固定为milestoneMinWidth
 */
import Milestone from './milestone/Milestone.vue';

export default {
    name: 'BaseLineSchedule',
    components: {
        Milestone
    },
    props: {
        // 里程碑数组
        wholeList: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
            // 每个里程碑最大延期天数组成的数组
            accumulation: [],
            // 里程碑最大长度
            milestoneMaxWidth: 450,
            // 算上虚线框的总宽度
            wholeWidth: [],
            // 进度条的宽度
            progressWidth: [],
            delay: []
        };
    },
    computed: {
        milestoneMinWidth() {
            return this.wholeList[0]?.projectDetailDashboardDetailList
                ?.length <= 4
                ? 220
                : 150;
        }
    },
    watch: {
        wholeList(newVal) {
            if (newVal.length > 0) {
                this.reset();
                this.getMilestoneInfo();
            }
        }
    },
    methods: {
        /**
         * 获取里程碑信息
         */
        getMilestoneInfo() {
            if (this.wholeList.length === 0) return;
            let delayDaysList = [];
            // 每个里程碑的延期天数先做一个数组
            this.wholeList.forEach((i) => {
                const res = i.projectDetailDashboardDetailList.map((j) => {
                    return j.detailDelayDays || 0;
                });
                delayDaysList.push(res);
            });
            this.delay = delayDaysList;
            this.$nextTick(() => {
                this.wholeWidth = this.getWholeWidth(delayDaysList);
                this.progressWidth = this.getProgressWidth(this.wholeWidth);
            });
        },
        /**
         * 重置
         */
        reset() {
            this.wholeWidth = [];
            this.progressWidth = [];
        },
        /**
         * 计算延期天数的累加值
         * @param {Array} delayDaysList 延期天数的数组
         */
        getAccmulationArr(delayDaysList) {
            const rows = delayDaysList.length;
            if (rows === 0) {
                return [];
            }
            const cols = delayDaysList[0].length;
            // 创建一个空数组，从底部开始累加
            let accumulation = Array.from({ length: rows }, () =>
                Array.from({ length: cols }, () => 0)
            );

            for (let col = 0; col < cols; col++) {
                let sum = 0;
                for (let row = rows - 1; row >= 0; row--) {
                    sum += delayDaysList[row][col];
                    accumulation[row][col] = sum;
                }
            }
            return accumulation;
        },
        /**
         * 获取里程碑的总宽度(包含虚线框的宽度)
         * @param {Array} delayDaysList 延期天数的数组
         */
        getWholeWidth(delayDaysList) {
            const accumulation = this.getAccmulationArr(delayDaysList);
            const wholeDelayDays = this.getAccmulationArr(accumulation)[0];
            if (accumulation.length === 0) return;
            // 根据累加值计算实际宽度，两部分宽度一部分是总宽度，一部分是进度条的宽度，
            // 进度条的宽度实际上就是上一个里程碑的总宽度，所以先算总宽度就行，
            const res = accumulation.map((i, i_Index) => {
                return (
                    i.map((j, j_Index) => {
                        // 以milestoneMinWidth为基准, 该宽度对应总延期天数的宽度
                        // v0里程碑一定是没有延期的，宽度就是最小宽度
                        if (i_Index === accumulation.length - 1) {
                            return this.milestoneMinWidth;
                        }
                        // accumulation[0][index] === 0代表这一整排加起来都是0，说明没有变更
                        if (accumulation[0][j_Index] === 0 && j === 0) {
                            return this.milestoneMinWidth;
                        }
                        // 总延期天数
                        const allDelayDays = wholeDelayDays[j_Index];
                        if (allDelayDays === 0) {
                            // 加上虚线框的宽度
                            return this.milestoneMinWidth + 50;
                        }
                        // 根据总延期天数的比例去拿长度,最小长度+比例长度
                        let ratioLength = 0;
                        if (accumulation.length >= 4) {
                            ratioLength =
                                (j / allDelayDays) *
                                2.5 *
                                this.milestoneMinWidth;
                        } else {
                            ratioLength =
                                (j / allDelayDays) * this.milestoneMinWidth;
                        }
                        let res = Math.floor(
                            this.milestoneMinWidth + ratioLength
                        );
                        return Math.floor(res);
                    }) || []
                );
            });
            this.delay.map((i, i_Index, arr) => {
                i.map((j, j_index) => {
                    if (j === 0) {
                        let temp = i_Index;
                        while (temp <= arr.length - 1) {
                            if (arr[temp][j_index] !== 0) {
                                res[i_Index][j_index] = res[temp][j_index];
                                break;
                            }
                            temp++;
                        }
                    }
                });
            });
            return res;
        },
        /**
         * 获取里程碑的进度条宽度,进度条宽度就是上一个里程碑的总宽度
         * @param {Array} wholeWidth 里程碑的总宽度
         */
        getProgressWidth(wholeWidth) {
            if (wholeWidth.length === 0) return;
            const res = this.$tools.cloneDeep(wholeWidth);
            res.shift();
            res.push(wholeWidth[wholeWidth.length - 1]);
            return res;
        }
    }
};
</script>
<style lang="scss" scoped>
.milestone-container {
    overflow-x: auto;
    width: 100%;
    height: calc(100vh - 150px);
}
.flex {
    display: flex;
}
.milestone-flag {
    width: 20px;
    height: 20px;
    margin: 0 5px 0px 5px;
}
.milestone-color-intro {
    display: flex;
    height: 20px;
    margin-left: 10px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    span {
        line-height: 20px;
    }
    .prefix-circle {
        height: 15px;
        width: 15px;
        border-radius: 50%;
        margin: 1px 5px 0 5px;
    }
}
.check-mark-flag {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #0064f0;
    border: 2px solid #fff;
    margin: 0 5px;
}
</style>
