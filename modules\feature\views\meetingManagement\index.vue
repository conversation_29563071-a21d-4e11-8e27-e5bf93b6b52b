<template>
    <div>
        <div class="box-main">
            <el-tabs v-model="activeName">
                <el-tab-pane
                    label="人员参会查询"
                    name="JudgeAttendanceSearch"
                    lazy
                >
                    <JudgeAttendanceQuery></JudgeAttendanceQuery>
                </el-tab-pane>
                <el-tab-pane label="会议查询" name="MeetingSearch" lazy>
                    <MeetingSearch></MeetingSearch>
                </el-tab-pane>
                <el-tab-pane
                    label="会议任务跟踪"
                    name="MeetingTaskTracker"
                    lazy
                >
                    <MeetingTaskTrace></MeetingTaskTrace>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>

<script>
import JudgeAttendanceQuery from './components/judgeAttendanceQuery';
import MeetingSearch from './components/meetingSearch';
import MeetingTaskTrace from './components/meetingTaskTrace';
import { getExternalStaffPeople } from 'feature/views/meetingManagement/commonFunction';

export default {
    name: 'MeetingManagement',
    components: {
        JudgeAttendanceQuery,
        MeetingSearch,
        MeetingTaskTrace
    },
    data() {
        return {
            activeName: 'MeetingTaskTracker'
        };
    },
    created() {
        // 缓存页面
        this.$store.dispatch('tagsView/addView', this.$route);
        getExternalStaffPeople(this);
    }
};
</script>
<style lang="scss" scoped>
.box-main {
    width: 100%;
    padding: 10px 20px 0 20px;
    background-color: #ffffff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
::v-deep #pane-second {
    border: 1px solid #8c8c8c !important;
}
</style>
