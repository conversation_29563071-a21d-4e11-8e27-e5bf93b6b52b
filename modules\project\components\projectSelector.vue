<template>
    <div class="top-selector-container">
        <div class="info-tag primary-tag">
            <el-button
                class="project-list-button"
                plain
                @click="$router.push('/project/projectList')"
            >
                项目列表
            </el-button>
        </div>
        <TopSelector
            ref="TopSelector"
            class="middle-tag"
            @lazyLoad="handleLazyLoad"
            :statusDisabled="statusDisabled"
            :infoDisabled="infoDisabled"
            @input="handleChange"
            :placeholder="placeholder"
            :value="value"
            :status="projectStatus"
            :key="key"
        ></TopSelector>
    </div>
</template>

<script>
/**
 * ProjectSelector 组件
 * @module project/components
 * @desc 用于选择项目的级联选择器，并支持懒加载数据。
 * 注意：因为要保证多页面选择项联动，如果需要赋值，使用this.$store.dispatch('project/changeProject', value);
 * 同时需要设置queryOnMount为false，然后在挂载时手动查询，否则会查询空值
 * @param {Boolean} [infoDisabled=false] - 控制级联选择器是否禁用，默认为 false
 * @param {String} [placeholder='请选择部门'] - 级联选择器的占位符文本，默认为"请选择小组"
 * @param {Array} [value=[]] - 级联选择器当前选中的值，默认为空数组
 * @param {Function} [input] - 选择值发生变化时触发的方法
 * @example 调用示例
 *  <ProjectSelector
 *    :infoDisabled="infoDisabled"
 *    :placeholder="placeholder"
 *    @input="handleChange"
 *  ></ProjectSelector>
 * */
import TopSelector from 'Components/TopSelector';

const initNodes = (scope) => {
    return scope.$store.state.project.productLine.map((item) => {
        const obj = {
            value: item.label,
            label: item.label,
            disabled: true
        };
        return obj;
    });
};
export default {
    name: 'ProjectSelector',
    components: {
        TopSelector
    },
    props: {
        infoDisabled: {
            type: Boolean,
            default: false
        },
        placeholder: {
            type: String,
            default: '请选择项目'
        },
        statusDisabled: {
            type: Boolean,
            default: true
        },
        projectStatus: {
            type: String,
            default: ''
        },
        queryOnMount: {
            type: Boolean,
            default: true
        }
    },
    data() {
        return {
            options: initNodes(this),
            currentStatus: '',
            key: 0,
            // 获取部门信息的接口
            getProjectInfo:
                this.$service.project.projectInfo.getProjectListByProductLine
        };
    },
    computed: {
        // 利用缓存控制value
        value() {
            return this.$store.state.project.projectStore || [];
        }
    },
    mounted() {
        // 挂载时触发事件，便于查询
        if (this.value.length !== 0) {
            this.queryOnMount && this.handleChange(this.value);
        }
    },
    methods: {
        /**
         * 获取第一级节点,
         * 注意:由于cascader内部的执行顺序问题,只能使用promise获取
         * @return {Promise} 获取节点的promise
         */
        getFirstLevel() {
            return new Promise((resolve) => {
                resolve(initNodes(this));
            });
        },
        /**
         * 处理节点懒加载
         * @param {Object} node 当前节点
         * @param {Function} resolve 加载成功后的回调
         * @return {Function} 加载成功后的回调
         */
        handleLazyLoad(node, resolve) {
            const { level, value } = node;
            // 一级节点为产品线列表（常量）
            if (level === 0) {
                this.getFirstLevel().then((res) => {
                    const firstLevel = res.map((item) => {
                        item.leaf = level >= 1;
                        return item;
                    });
                    return resolve(firstLevel);
                });
            } else if (level === 1) {
                const params = { productLine: value };
                const api = this.getProjectInfo;
                api(params)
                    .then((res) => {
                        if (res.head.code !== '000000') {
                            this.$message.error(res.head.message);
                            return;
                        }
                        // 构建节点数组
                        const nodes = res.body.map((item) => {
                            const obj = {
                                value: item.projectId,
                                label: item.projectName,
                                status: item.projectStatus,
                                proProjectId: item.proProjectId,
                                leaf: level >= 1,
                                disabled: level === 0
                            };
                            return obj;
                        });
                        // 加载成功，返回数据
                        resolve(nodes);
                    })
                    .catch((error) => {
                        console.error(error.message);
                    });
            } else if (level >= 2) {
                return resolve();
            }
        },
        /**
         *  向上传递选中值后的回调
         * @param {Array} value 选中的值
         */
        handleChange(value) {
            if (!value) return;

            // 获取选中的节点
            const checkedNodes =
                this.$refs.TopSelector.$refs.cascaderRef?.getCheckedNodes();

            // 如果有选中的节点，并且是项目级别的节点(第二级)
            if (checkedNodes && checkedNodes.length > 0) {
                // 获取最后一个节点（即项目节点）
                const projectNode = checkedNodes[0];
                if (projectNode) {
                    const { proProjectId, label, status } = projectNode.data;
                    this.$store.dispatch('project/changeProjectInfo', {
                        projectName: label,
                        proProjectId,
                        projectStatus: status
                    });
                }
            }

            this.$store.dispatch('project/changeProject', value);
            this.$emit('input', value);
        }
    }
};
</script>
<style lang="scss" scoped>
.top-selector-container {
    height: 45px;
    display: flex;
    align-items: center;
    margin: 10px 0 7px 16px;
}
.project-list-button {
    height: 34px;
    margin: 0 10px;
    flex: 1;
    font-weight: 400;
}
.primary-tag {
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
}
.info-tag {
    height: 45px;
    display: flex;
    background: #f0f0f0;
    border-bottom: 1px solid #d8dce5;
    box-shadow: 0px 1px 3px 0 rgba(0, 0, 0, 0.12),
        0px 0 3px 0 rgba(0, 0, 0, 0.04);
    justify-content: center;
    align-items: center;
    position: relative;
    .selector {
        margin: 0 10px 0 20px;
        flex: 1;
        ::v-deep .el-input--mini .el-input__inner {
            line-height: 34px;
            height: 34px;
            font-weight: 400;
        }
    }
}
.info-tag::after {
    content: '';
    position: absolute;
    right: -14px;
    top: 0px;
    width: 0;
    height: 0;
    border-top: 23px solid transparent;
    border-bottom: 23px solid transparent;
    border-left: 15px solid #f0f0f0;
}
.middle-tag {
    position: relative;

    ::v-deep .infoTag {
        box-shadow: 0px 1px 3px 0 rgba(0, 0, 0, 0.12),
            0px 0 3px 0 rgba(0, 0, 0, 0.04);
    }
    ::v-deep .infoTag .selector {
        margin-left: 20px;
    }
}
.middle-tag::before {
    z-index: 2;
    content: '';
    position: absolute;
    left: -2px;
    top: 0px;
    width: 0;
    height: 0;
    border-top: 23px solid transparent;
    border-bottom: 23px solid transparent;
    border-left: 15px solid #fff;
}
</style>
