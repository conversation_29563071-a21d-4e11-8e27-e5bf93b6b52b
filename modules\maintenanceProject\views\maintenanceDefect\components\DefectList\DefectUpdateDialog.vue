<template>
    <div>
        <el-dialog
            :title="title"
            :visible.sync="dialogVisible"
            :before-close="reset"
            width="70%"
        >
            <el-form
                ref="form"
                :model="form"
                class="form"
                size="small"
                :rules="rules"
                label-width="130px"
            >
                <el-divider>基本信息</el-divider>
                <el-form-item label="缺陷标题" prop="faultTitle">
                    <el-input
                        v-model="form.faultTitle"
                        placeholder="请输入缺陷标题"
                        :disabled="form.faultSource === '电子流（顾客投诉OA）'"
                    ></el-input>
                </el-form-item>
                <div class="flex">
                    <el-form-item
                        class="w-50"
                        label="缺陷状态"
                        prop="faultStatus"
                    >
                        <el-select
                            v-model="form.faultStatus"
                            placeholder="请选择缺陷状态"
                        >
                            <el-option
                                v-for="item in CONSTANTS.DEFECT_STATUS"
                                :label="item"
                                :key="item"
                                :value="item"
                            ></el-option
                        ></el-select>
                    </el-form-item>
                    <el-form-item label="项目经理" prop="projectManager">
                        <PeopleSelector
                            ref="projectManagerName"
                            v-model="form.projectManager"
                            :isAll="1"
                            :isMultipled="false"
                            :disabled="true"
                        ></PeopleSelector>
                    </el-form-item>
                </div>
                <div class="flex" style="height: 58px">
                    <el-form-item
                        class="w-50"
                        label="缺陷来源"
                        prop="faultSource"
                    >
                        <el-select
                            v-if="form.faultSource !== '电子流（顾客投诉OA）'"
                            v-model="form.faultSource"
                            placeholder="请选择缺陷来源"
                        >
                            <el-option
                                v-for="item in defectSourceList.filter(
                                    (i) => i.value !== '电子流（顾客投诉OA）'
                                )"
                                :label="item.label"
                                :key="item.value"
                                :value="item.value"
                            ></el-option
                        ></el-select>
                        <div v-else>{{ form.faultSource }}</div>
                    </el-form-item>
                    <el-form-item
                        label="OA流程状态"
                        prop="flowStatus"
                        v-if="form.faultSource === '电子流（顾客投诉OA）'"
                    >
                        {{ form.flowStatus }}
                    </el-form-item>
                </div>
                <div class="flex">
                    <el-form-item
                        class="w-50"
                        label="关联流程ID"
                        prop="flowNo"
                        v-if="form.faultSource === '电子流（顾客投诉OA）'"
                    >
                        {{ form.flowNo }}
                    </el-form-item>
                    <el-form-item
                        v-if="form.faultSource === '电子流（顾客投诉OA）'"
                        class="w-50"
                        label="关联客户"
                        prop="customerName"
                    >
                        {{ form.customerName }}
                    </el-form-item>
                </div>
                <div class="flex">
                    <el-form-item
                        class="w-50"
                        label="产品型号"
                        prop="productModel"
                    >
                        <el-input
                            v-model="form.productModel"
                            placeholder="请输入产品型号"
                            @blur="handleProjectModelBlur"
                            :disabled="
                                form.faultSource === '电子流（顾客投诉OA）'
                            "
                        ></el-input>
                    </el-form-item>
                    <el-form-item
                        class="w-50"
                        label="产品名称"
                        prop="productName"
                    >
                        <el-input
                            v-model="form.productName"
                            placeholder="请输入产品名称"
                        ></el-input>
                    </el-form-item>
                </div>

                <div class="flex">
                    <el-form-item
                        label="归属产品线"
                        prop="productLine"
                        key="productLine"
                        class="w-50"
                    >
                        <el-select
                            v-model="form.productLine"
                            placeholder="请选择归属产品线"
                            size="medium"
                            @change="handleProductLineChange"
                        >
                            <el-option
                                v-for="item in productLineList"
                                :key="item.label"
                                :label="item.label"
                                :value="item.label"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item
                        label="归属细分产品线"
                        prop="subProductLine"
                        key="subProductLine"
                    >
                        <el-select
                            v-model="form.subProductLine"
                            placeholder="请选择归属细分产品线"
                            size="medium"
                        >
                            <el-option
                                v-for="item in subProductLineOptions"
                                :key="item.paramName"
                                :label="item.paramName"
                                :value="item.paramName"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </div>

                <el-divider>缺陷识别</el-divider>
                <div class="flex">
                    <el-form-item
                        class="w-50"
                        label="发现时间"
                        prop="applyTime"
                    >
                        <el-date-picker
                            v-model="form.applyTime"
                            type="date"
                            placeholder="请选择发现日期"
                            value-format="yyyy-MM-dd"
                            :disabled="
                                form.faultSource === '电子流（顾客投诉OA）'
                            "
                        ></el-date-picker>
                    </el-form-item>
                    <el-form-item label="缺陷严重等级" prop="faultLevel">
                        <el-select
                            v-model="form.faultLevel"
                            placeholder="请选择缺陷严重等级"
                        >
                            <el-option
                                v-for="item in CONSTANTS.DEFECT_LEVEL"
                                :label="item"
                                :key="item"
                                :value="item"
                            ></el-option
                        ></el-select>
                    </el-form-item>
                </div>
                <div class="flex">
                    <el-form-item
                        v-if="form.faultSource === '电子流（顾客投诉OA）'"
                        class="w-50"
                        label="问题定义"
                        prop="problemDefinition"
                    >
                        {{ form.problemDefinition }}
                    </el-form-item>
                    <el-form-item
                        label="缺陷占比"
                        prop="faultProp"
                        style="height: 58px"
                    >
                        <el-input
                            v-model="form.faultProp"
                            :disabled="
                                form.faultSource === '电子流（顾客投诉OA）'
                            "
                            :clearable="false"
                        >
                            <span slot="suffix">%</span>
                        </el-input>
                    </el-form-item>
                </div>
                <el-form-item label="缺陷描述" prop="problemOverview">
                    <el-input
                        type="textarea"
                        v-model="form.problemOverview"
                        placeholder="请输入缺陷描述"
                        :autosize="{ minRows: 4, maxRows: 8 }"
                    ></el-input>
                </el-form-item>

                <el-divider>缺陷分析与应对</el-divider>
                <el-form-item label="根因分析" prop="causeAnalyse">
                    <el-input
                        type="textarea"
                        v-model="form.causeAnalyse"
                        placeholder="请输入根因分析"
                        :autosize="{ minRows: 4, maxRows: 8 }"
                    ></el-input>
                </el-form-item>
                <el-form-item label="临时解决措施" prop="solutionMeasuresTemp">
                    <el-input
                        type="textarea"
                        v-model="form.solutionMeasuresTemp"
                        placeholder="请输入解决措施"
                        :autosize="{ minRows: 4, maxRows: 8 }"
                    ></el-input>
                </el-form-item>
                <el-form-item label="永久解决措施" prop="solutionMeasures">
                    <el-input
                        type="textarea"
                        v-model="form.solutionMeasures"
                        placeholder="请输入解决措施"
                        :autosize="{ minRows: 4, maxRows: 8 }"
                    ></el-input>
                </el-form-item>
                <el-form-item
                    label="已上升技术委员会"
                    prop="whetherAscendTechCommittee"
                    label-width="150px"
                >
                    <el-radio-group v-model="form.whetherAscendTechCommittee">
                        <el-radio label="是">是</el-radio>
                        <el-radio label="否">否</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item
                    class="w-100"
                    label="解决人"
                    prop="resolvePersonAccountList"
                >
                    <PeopleSelector
                        ref="resolvePersonList"
                        class="w-100"
                        v-model="form.resolvePersonAccountList"
                        placeholder="请选择解决人"
                        :isAll="1"
                    ></PeopleSelector>
                </el-form-item>
                <el-form-item label="缺陷解决计划" prop="faultPlanType">
                    <el-radio-group
                        v-model="form.faultPlanType"
                        :disabled="faultPlanTypeDisabled"
                    >
                        <el-radio label="不需要创建">不需要创建</el-radio>
                        <el-radio label="需要创建">需要创建</el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item
                    label="关联禅道项目"
                    prop="proProjectId"
                    v-show="form.faultPlanType === '需要创建'"
                    :rules="form.faultPlanType === '需要创建' ? required : {}"
                >
                    <el-select
                        class="w-100"
                        v-model="form.proProjectId"
                        placeholder="请选择关联禅道项目"
                        :disabled="faultPlanTypeDisabled"
                        @change="getZentaoTaskList"
                        filterable
                    >
                        <el-option
                            v-for="item in zentaoProjectList"
                            :label="item.name"
                            :key="item.id"
                            :value="item.id"
                        ></el-option
                    ></el-select>
                </el-form-item>
                <el-form-item
                    label="关联禅道任务"
                    prop="proTaskId"
                    v-if="form.faultPlanType === '需要创建' && form.proTaskId"
                >
                    <div class="flex space-between">
                        <el-select
                            v-if="form.newProTaskId && form.newProTaskId !== 0"
                            class="w-100"
                            v-model="form.newProTaskId"
                            disabled
                            filterable
                        >
                            <el-option
                                v-for="item in zentaoTaskList"
                                :label="item.name"
                                :key="item.id"
                                :value="item.id"
                            ></el-option
                        ></el-select>
                        <div v-else-if="form.newProTaskId === 0"></div>
                        <el-select
                            v-else-if="form.proTaskId"
                            class="w-100"
                            v-model="form.proTaskId"
                            disabled
                            filterable
                        >
                            <el-option
                                v-for="item in zentaoTaskList"
                                :label="item.name"
                                :key="item.id"
                                :value="item.id"
                            ></el-option
                        ></el-select>
                        <div v-else></div>
                        <ChangeTaskDialog
                            v-if="form.proProjectId"
                            :hasTaskHour="consumed !== '0.0'"
                            :zentaoProjectList="zentaoProjectList"
                            @save="handleChangeTaskSave"
                            class="pl-20"
                        ></ChangeTaskDialog>
                    </div>
                </el-form-item>
            </el-form>
            <div class="footer" slot="footer">
                <el-tooltip
                    :key="riskToolTipKey"
                    effect="dark"
                    content="该缺陷已关联风险，删除关联的风险后可删除该缺陷。"
                    placement="top"
                    :disabled="form.hasRisk !== '是'"
                >
                    <el-button
                        v-if="type === 'edit'"
                        type="danger"
                        @click="handleDelete"
                        :disabled="
                            form.faultSource === '电子流（顾客投诉OA）' ||
                            form.hasRisk === '是'
                        "
                        >删 除</el-button
                    >
                </el-tooltip>
                <el-button @click="closeDialog">取 消</el-button>
                <el-button type="primary" @click="save">保 存</el-button>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { CONSTANTS } from '@/constants';
import PeopleSelector from 'Components/PeopleSelector';
import {
    getSelectedLabel,
    getUserAccount
} from 'feature/views/meetingManagement/commonFunction';
import ChangeTaskDialog from 'maintenanceProject/components/ChangeTaskDialog/index.vue';
import { rules, required } from './formInit.js';

export default {
    name: 'DefectUpdateDialog',
    components: { PeopleSelector, ChangeTaskDialog },
    props: {
        visible: {
            type: Boolean,
            default: false
        },
        type: {
            type: String,
            default: 'add'
        },
        id: {
            type: String,
            default: ''
        },
        productLine: {
            type: String,
            default: ''
        },
        subProductLine: {
            type: String,
            default: ''
        }
    },

    data() {
        return {
            productLineList: this.$store.state.project.productLine,
            rules,
            required,
            form: {
                id: '',
                faultTitle: '',
                faultStatus: '激活',
                projectManager: getUserAccount(),
                projectManagerName: '',
                faultSource: '',
                flowStatus: '',
                flowNo: '',
                customerName: '',
                productModel: '',
                productName: '',
                productLine: '',
                subProductLine: '',
                applyTime: '',
                faultLevel: '',
                problemDefinition: '',
                faultProp: '',
                problemOverview: '',
                causeAnalyse: '',
                solutionMeasuresTemp: '',
                solutionMeasures: '',
                whetherAscendTechCommittee: '否',
                resolvePersonAccountList: [],
                faultPlanType: '不需要创建',
                proProjectId: '',
                hasRisk: '是'
            },
            CONSTANTS,
            zentaoProjectList: [],
            zentaoTaskList: [],
            subProductLineOptions: [],
            defectSourceList: [],
            faultPlanTypeDisabled: false,
            // 当前任务已消耗工时
            consumed: '0.0',
            riskToolTipKey: 0
        };
    },
    computed: {
        dialogVisible: {
            get() {
                return this.visible;
            },
            set(value) {
                this.$emit('update:visible', value);
            }
        },
        externalOptions() {
            return this.$store.state.feature.externalStaff;
        },
        title() {
            return this.type === 'add' ? '新建缺陷' : '编辑缺陷';
        }
    },
    watch: {
        dialogVisible(newVal) {
            if (newVal) {
                this.getDefectSourceOptions();
                // 编辑的时候先查详情，后根据详情的项目经理获取禅道项目
                if (this.id) {
                    this.getDetail();
                } else {
                    this.getZentaoProjectList();
                    this.form.id = '';
                }
            }
        }
    },
    methods: {
        /**
         * 关闭弹窗
         */
        closeDialog() {
            this.$refs.form.resetFields();
            this.faultPlanTypeDisabled = false;
            this.form.proTaskId = '';
            this.dialogVisible = false;
        },
        /**
         * 获取缺陷详情
         */
        async getDetail() {
            const api = this.$service.maintenanceProject.defect.getDetail;
            const params = {
                id: this.id
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    Object.keys(res.body).forEach((key) => {
                        this.$set(this.form, key, res.body[key]);
                    });
                    this.riskToolTipKey += 1;
                    // 如果已经关联了禅道项目，就禁用掉
                    this.faultPlanTypeDisabled =
                        this.form.faultPlanType === '需要创建';
                    this.form.proProjectId && this.getTaskList();
                    if (this.type === 'edit') {
                        this.getZentaoProjectList();
                        this.getZentaoTaskList();
                    }
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 保存
         */
        async save() {
            const valid = await this.validForm();
            if (!valid) {
                this.$message.warning('请输入所有必填项');
                return;
            }
            if (this.form.faultPlanType === '不需要创建') {
                this.form.proProjectId = '';
            }
            if (this.type === 'add') {
                const subProductLine = this.subProductLine
                    ? `的<b>${this.subProductLine}</b>细分产品线`
                    : '';
                const tip = `您现在正在<b>${this.productLine}</b>产品线维护项目${subProductLine}下创建风险数据，如果归属的产品线信息正确，
            请点击确认继续创建，如果不正确请联系管理员处理。`;
                await this.$confirm(tip, '提示', {
                    type: 'warning',
                    dangerouslyUseHTMLString: true
                });
            }
            const api = this.$service.maintenanceProject.defect.updateDefect;
            const projectManagerName = getSelectedLabel(
                this.$refs.projectManagerName
            );
            const resolvePersonList = getSelectedLabel(
                this.$refs.resolvePersonList
            );
            // 编辑的时候，如果此时没有创建任务，并且选择了需要创建
            // 将 newProTaskId 改为 0
            if (
                !this.form.proTaskId &&
                this.form.faultPlanType === '需要创建'
            ) {
                this.form.newProTaskId = 0;
            }
            try {
                const res = await api({
                    ...this.form,
                    projectManagerName,
                    resolvePersonList,
                    // 流程软硬件归属，固定为硬件
                    flowSoftHardBelong: '硬件'
                });
                if (res.head.code === '000000') {
                    this.$message.success('保存成功');
                    this.$emit('success');
                    this.closeDialog();
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 校验
         */
        async validForm() {
            try {
                await this.$refs.form.validate();
                return true;
            } catch (error) {
                return false;
            }
        },
        /**
         * 关闭弹窗前的回调
         * @param {Function} done 关闭弹窗的函数
         */
        reset(done) {
            this.$refs.form.resetFields();
            this.faultPlanTypeDisabled = false;
            this.form.proTaskId = '';
            done();
        },
        /**
         * 产品型号失焦时，进行查询获取产品线/细分产品线/产品名称
         * 获取到之后，将对应的空的选项填入获取到的值
         * 非空选项不变
         */
        async handleProjectModelBlur() {
            if (!this.form.productModel) return;
            const api =
                this.$service.maintenanceProject.common
                    .getProductInfoByProductModel;
            const params = {
                productModel: this.form.productModel
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    const { productLine, subProductLine, productName } =
                        res.body;
                    if (productLine) {
                        this.form.productLine = productLine;
                    }
                    if (subProductLine) {
                        this.form.subProductLine = subProductLine;
                    }
                    if (productName) {
                        this.form.productName = productName;
                    }
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 获取禅道项目下拉列表
         */
        async getZentaoProjectList() {
            const api =
                this.$service.maintenanceProject.zentao.getZentaoProjectList;
            const params = {
                projectCode: '维护',
                projectManagerAccount: this.id
                    ? this.form.projectManager
                    : getUserAccount()
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.zentaoProjectList = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 根据项目获取子任务接口
         * @param {String} value id
         */
        async getZentaoTaskList() {
            if (!this.form.proTaskId) return;
            const api =
                this.$service.maintenanceProject.zentao.getZentaoTaskList;
            try {
                const res = await api({ proProjectId: this.form.proProjectId });
                if (res.head.code === '000000') {
                    this.zentaoTaskList = res.body;
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 产品线变更后，根据产品线查询细分产品线
         * @param {String} value 产品线
         */
        handleProductLineChange(value) {
            this.form.subProductLine = '';
            this.getSubProductLineOptions(value);
        },
        /**
         * 获取细分产品线对应的选项
         * @param {String} value 产品线
         */
        async getSubProductLineOptions(value) {
            const api = this.$service.project.finance.getSelectOptions;
            const params = { paramName: '细分产品线', paramType: value };
            const res = await api(params);
            if (res.head.code !== '000000') {
                this.$message.error(res.head.message);
                return;
            }
            this.subProductLineOptions = res.body;
        },
        /**
         * 获取缺陷来源对应的选项
         * @param {String} value 产品线
         */
        async getDefectSourceOptions() {
            const api = this.$service.project.finance.getSelectOptions;
            const params = { paramName: '缺陷来源', paramType: '' };
            const res = await api(params);
            if (res.head.code !== '000000') {
                this.$message.error(res.head.message);
                return;
            }
            this.defectSourceList = res.body.map((i) => ({
                value: i.paramName,
                label: i.paramName
            }));
        },
        handleChangeTaskSave(value) {
            this.form.proProjectId = value.proProjectId;
            this.form.newProTaskId = value.proTaskId;
            this.getZentaoTaskList();
        },
        /**
         * 删除缺陷
         */
        async handleDelete() {
            await this.$confirm('确定删除吗?', '提示', { type: 'warning' });
            const api = this.$service.maintenanceProject.defect.deleteDefect;
            const params = {
                id: this.form.id,
                proTaskId: this.form.proTaskId
            };
            try {
                const res = await api(params);
                if (res.head.code === '000000') {
                    this.$message.success('删除成功');
                    const { name } = this.$route;

                    if (name === 'MaintenanceDefectDetail') {
                        history.go(-1);
                        return;
                    }
                    this.$emit('success');
                    this.closeDialog();
                } else {
                    this.$message.error(res.head.message);
                }
            } catch (error) {
                console.error(error);
            }
        },
        /**
         * 获取禅道任务列表
         */
        async getTaskList() {
            const api =
                this.$service.maintenanceProject.zentao
                    .getZentaoTaskListInProject;
            const params = {
                id: this.form.proTaskId,
                proProjectId: this.form.proProjectId
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                // 获取该任务消耗工时
                if (res.body.length > 0) {
                    const { consumed = null } = res.body[0];
                    this.consumed = consumed;
                } else {
                    this.consumed = '0.0';
                }
            } catch (error) {
                console.error(error, 'error');
            }
        }
    }
};
</script>

<style lang="scss" scoped>
.flex {
    display: flex;
}
.w-50 {
    width: 50%;
}
.w-100 {
    width: 100%;
}
.space-between {
    justify-content: space-between;
}
.ml-10 {
    margin-left: 10px;
}
.minutes {
    margin-top: 15px;
    justify-content: flex-start;
}
.footer {
    display: flex;
    justify-content: center;
    gap: 15px;
}
.pl-20 {
    padding-left: 20px;
}
::v-deep .form .el-form-item__label {
    font-weight: bold;
}
</style>
