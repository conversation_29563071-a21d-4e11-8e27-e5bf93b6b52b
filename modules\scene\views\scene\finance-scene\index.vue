<template>
    <div
        class="page-view"
        id="scene-view"
        @scroll="onScroll"
        ref="financeOuterRef"
    >
        <AddProjectByScene
            businessUnit="金融机具"
            :productData="productData"
        ></AddProjectByScene>
        <DeleteProject
            businessUnit="金融机具"
            :productData="productData"
        ></DeleteProject>
        <nav-tabs-vue
            class="tab-view"
            :which-index="whichIndex"
            :component-id="componentId"
            :cardArr="cardArr"
            @tab-click="handleTabClick"
        ></nav-tabs-vue>
        <product-overview
            v-if="componentId === 'ProductOverview'"
        ></product-overview>
        <domestic-finance-page
            v-if="componentId === 'Withindomestic'"
        ></domestic-finance-page>
        <overseas-finance-page
            v-if="componentId === 'Withinoverseas'"
        ></overseas-finance-page>
        <overseas-retail-page
            v-if="componentId === 'Overseascommercial'"
        ></overseas-retail-page>
        <el-button type="primary" @click="goback" class="goback-button"
            >返回</el-button
        >
    </div>
</template>
<script>
import NavTabsVue from '../components/NavTabs.vue';
import ProductOverview from './components/ProductOverview.vue';
import DomesticFinancePage from './components/DomesticFinancePage.vue';
import OverseasFinancePage from './components/OverseasFinancePage.vue';
import OverseasRetailPage from './components/OverseasRetailPage.vue';
import AddProjectByScene from 'scene/views/scene/components/AddProjectByScene';
import DeleteProject from 'scene/views/scene/components/DeleteProject.vue';

export default {
    name: 'FinanceScene',
    components: {
        NavTabsVue,
        ProductOverview,
        DomesticFinancePage,
        OverseasFinancePage,
        OverseasRetailPage,
        AddProjectByScene,
        DeleteProject
    },
    data() {
        return {
            whichIndex: 0,
            componentId: 'ProductOverview',
            cardArr: [
                {
                    componentName: '产品综述',
                    componentId: 'ProductOverview'
                },
                {
                    componentName: '国内网点',
                    componentId: 'Withindomestic'
                },
                {
                    componentName: '海外网点',
                    componentId: 'Withinoverseas'
                },
                {
                    componentName: '商业零售',
                    componentId: 'Overseascommercial'
                }
            ]
        };
    },
    computed: {
        productData() {
            return this.$store.state.scene.financeSceneProductStore;
        }
    },
    mounted() {
        // 缓存页面
        this.$store.dispatch('tagsView/addView', this.$route);
    },
    methods: {
        handleTabClick(index, componentId) {
            this.whichIndex = index;
            this.componentId = componentId;
        },
        goback() {
            this.$router.push('/dashboard-index');
        },
        /**
         * 滚动时清除弹窗
         */
        onScroll() {
            document
                .querySelectorAll('.resources-sceneNode--popper')
                .forEach((el) => el.remove());
            document
                .querySelectorAll('.resources-product-chart--popper')
                .forEach((el) => el.remove());
        }
    }
};
</script>
<style lang="scss" scoped>
.page-view {
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    overflow-y: auto;
    position: relative;
    transform-origin: 0 0;
    background: url(~scene/assets/product-overview-page-bg.png) no-repeat;
    background-size: cover;
}
.tab-view {
    position: absolute;
    top: 10px;
    z-index: 1;
    cursor: pointer;
}
.goback-button {
    position: absolute;
    top: 15px;
    right: 15px;
    z-index: 100;
}
</style>
