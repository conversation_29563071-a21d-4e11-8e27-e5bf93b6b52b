<template>
    <div class="defect-list-container">
        <div class="flex">
            <CollapsibleSearchPanel
                :queryParams="queryParams"
                :queryConfig="queryConfig"
                :isDot="isDot"
                :navItems="navItems"
                v-model="activeField"
                @navChange="handleNavChange"
                @search="handleSearch"
                @reset="handleReset"
            >
            </CollapsibleSearchPanel>
        </div>
        <div class="content-area">
            <el-table :data="tableData" height="calc(100vh - 230px)">
                <el-table-column
                    prop="index"
                    label="序号"
                    align="center"
                    width="60"
                ></el-table-column>
                <el-table-column
                    prop="applyTime"
                    label="OA发起时间"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column label="OA流程" align="center" width="220">
                    <template slot-scope="scope">
                        <el-tooltip
                            effect="dark"
                            :content="scope.row.flowNo"
                            placement="top"
                            popper-class="maintananceProject-tooltip-width"
                        >
                            <a
                                @click="handleRoutingJump(scope.row)"
                                class="overview-ellipsis"
                            >
                                {{ scope.row.flowNo }}
                            </a>
                        </el-tooltip>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="customerName"
                    label="客户"
                    align="center"
                    min-width="70"
                ></el-table-column>
                <el-table-column
                    prop="productModel"
                    label="产品型号"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column
                    prop="orderRequire"
                    label="订单要求"
                    header-align="center"
                    min-width="120"
                ></el-table-column>
                <el-table-column
                    prop="projectManagerName"
                    label="项目经理"
                    align="center"
                    width="120"
                ></el-table-column>
                <el-table-column label="操作" align="center" width="120">
                    <template slot-scope="scope">
                        <div class="flex flex-center gap-5"
                        style="height: 31px"
                        >
                            <!-- 关联禅道项目 -->
                            <el-popover
                                v-if="scope.row.proTaskId"
                                class="mt-5"
                                width="700"
                                trigger="hover"
                                @show="showZentaoTaskList(scope.row)"
                                @hide="hideZentaoTaskList"
                            >
                                <ZentaoTaskList
                                    :show="isShowZentaoTaskList"
                                    :proProjectId="scope.row.proProjectId"
                                    :proTaskId="scope.row.proTaskId"
                                    :projectManagerAccount="
                                        projectManagerAccount
                                    "
                                ></ZentaoTaskList>
                                <svg-icon
                                    slot="reference"
                                    icon-class="maintenanceProject-creat-task"
                                    class="task-icon blue"
                                    @click="handleCreateTask(scope.row)"
                                />
                            </el-popover>
                            <el-tooltip
                                v-if="!scope.row.proTaskId"
                                effect="dark"
                                content="无关联禅道任务"
                                placement="top"
                            >
                                <svg-icon
                                    icon-class="maintenanceProject-creat-task"
                                    class="task-icon gray"
                                    @click="handleCreateTask(scope.row)"
                                />
                            </el-tooltip>
                            <!-- 新建风险 -->
                            <el-popover
                                v-if="scope.row.whetherExistRisk === '是'"
                                class="mt-5"
                                width="700"
                                trigger="hover"
                                @show="() => riskListCounter++"
                            >
                                <RiskList
                                    :assObjectId="scope.row.orderId"
                                    :riskListCounter="riskListCounter"
                                    :relatedObject="relatedObject"
                                ></RiskList>
                                <svg-icon
                                    slot="reference"
                                    icon-class="maintenanceProject-creat-risk"
                                    class="risk-icon blue"
                                />
                            </el-popover>
                            <el-tooltip
                                v-if="scope.row.whetherExistRisk === '否'"
                                effect="dark"
                                content="无风险"
                                placement="top"
                            >
                                <svg-icon
                                    icon-class="maintenanceProject-creat-risk"
                                    class="risk-icon gray"
                                    @click="handleCreateRisk(scope.row)"
                                />
                            </el-tooltip>
                            <!-- 编辑缺陷 -->
                            <svg-icon
                                v-permission="['projectOrderUpdate']"
                                icon-class="maintenanceProject-edit"
                                class="edit-icon"
                                @click="handleOrderEdit(scope.row)"
                            />
                        </div>
                    </template>
                </el-table-column>
            </el-table>
            <pagination
                class="pagination"
                :total="total"
                :page.sync="page"
                :limit.sync="limit"
                @pagination="queryParamsSelect"
            />
        </div>
        <!-- 新建/编辑缺陷弹窗 -->
        <OrderUpdateDialog
            :visible.sync="orderUpdateDialogVisible"
            :id="id"
            @success="queryParamsSelect"
        ></OrderUpdateDialog>
        <!-- 建立禅道任务弹窗 -->
        <TargetZentaoTaskDialog
            :visible.sync="targetZentaoTaskDialogVisible"
            :projectManager="projectManagerAccount"
            @save="createZentaoTask"
            :projectType="projectType"
            :isOrderOrRisk="true"
        ></TargetZentaoTaskDialog>
        <!-- 新建风险弹窗 -->
        <RiskUpdateDialog
            type="add"
            :relatedObjectId="id"
            :relatedObject="relatedObject"
            :visible.sync="riskUpdateDialogVisible"
            @success="queryParamsSelect"
            :projectType="riskProjectType"
            :assProjectName="projectName"
            :assProjectId="projectId"
            :proProjectId="proProjectId"
        ></RiskUpdateDialog>
    </div>
</template>

<script>
import CollapsibleSearchPanel from 'Components/CollapsibleSearchPanel.vue';
import OrderUpdateDialog from './OrderUpdateDialog.vue';
import TargetZentaoTaskDialog from 'maintenanceProject/components/TargetZentaoTaskDialog/index.vue';
import ZentaoTaskList from 'maintenanceProject/components/ZentaoTaskList';
import RiskList from 'maintenanceProject/components/RiskList';
import RiskUpdateDialog from 'maintenanceProject/components/RiskUpdateDialog';
import { CONSTANTS } from '@/constants';
import { queryParams, queryConfig, navItems } from './formInit.js';

const oriQureryParams = {
    orderPlanType: '',
    flowStatus: '',
    customerName: '',
    productModel: '',
    whetherExistRisk: ''
};

export default {
    name: 'OrderList',
    components: {
        CollapsibleSearchPanel,
        OrderUpdateDialog,
        TargetZentaoTaskDialog,
        RiskUpdateDialog,
        ZentaoTaskList,
        RiskList
    },
    props: {
        // 当前页签
        activeName: {
            type: String,
            default: ''
        },
        productLine: {
            type: String,
            default: ''
        },
        subProductLine: {
            type: String,
            default: ''
        },
        projectManager: {
            type: String,
            default: ''
        },
        // 项目类型（开发/维护）
        projectType: {
            type: String,
            default: '维护'
        },
        // 项目ID(这时为开发项目)
        projectId: {
            type: String,
            default: ''
        },
        // 当前开发项目的名称
        projectName: {
            type: String,
            default: ''
        },
        // 当前开发项目对应的禅道项目
        proProjectId: {
            type: [String, Number],
            default: ''
        }
    },
    data() {
        return {
            total: 100,
            page: 1,
            limit: 50,
            queryForm: {
                currentPage: 1,
                pageSize: 50,
                // 是否存在禅道接口库
                faultPlanType: '',
                faultSource: '',
                faultStatus: '',
                faultTitle: '',
                whetherExistRisk: '',
                id: '',
                productModel: '',
                whetherAscendTechCommittee: ''
            },
            tableData: [],
            // 默认查询条件
            activeField: '存在风险的',
            navItems,
            // 顶部级联组件key
            projectSelectorKey: 0,
            // 建立禅道任务弹窗
            targetZentaoTaskDialogVisible: false,
            // 编辑缺陷弹窗
            orderUpdateDialogVisible: false,
            // 新建风险弹窗
            riskUpdateDialogVisible: false,
            // 编辑订单以及建立禅道任务按钮权限
            showOrderUpdatePermission:
                this.$store.state.permission.btnDatas.includes(
                    'projectOrderUpdate'
                ),
            // 新建风险按钮权限
            showRiskPermission: this.$store.state.permission.btnDatas.includes(
                'maintenanceProjectRiskUpdate'
            ),
            queryParams,
            queryConfig,
            CONSTANTS,
            // 是否正在查询
            isQuerying: false,
            // 当前的订单ID
            id: '',
            // 是否展示禅道任务列表
            isShowZentaoTaskList: false,
            // 项目经理域账号
            projectManagerAccount: '',
            // 当前选择的行的项
            rowData: {},
            riskListCounter: 0
        };
    },
    computed: {
        isDot() {
            return (
                Object.values(this.queryParams).findIndex((i) => i !== '') !==
                -1
            );
        },
        // 产品型号下拉选项
        productModelOptions() {
            const res = [];
            this.tableData.forEach((i) => {
                if (i.productModel) {
                    res.push(i.productModel);
                }
            });
            return [...new Set(res)].map((i) => ({
                value: i,
                label: i
            }));
        },
        // 客户名称下拉选项
        customerNameOptions() {
            const res = [];
            this.tableData.forEach((i) => {
                if (i.customerName) {
                    res.push(i.customerName);
                }
            });
            return [...new Set(res)].map((i) => ({
                value: i,
                label: i
            }));
        },
        riskProjectType() {
            return this.projectType === '开发' ? 'develop' : 'maintenance';
        },
        relatedObject() {
            return this.projectType === '开发' ? '开发项目订单' : '订单';
        }
    },
    watch: {
        productLine(newVal) {
            if (this.activeName === 'orderList') {
                this.queryParamsSelect();
            }
        },
        subProductLine(newVal) {
            if (this.activeName === 'orderList') {
                this.queryParamsSelect();
            }
        },
        projectManager(newVal) {
            if (this.activeName === 'orderList') {
                this.queryParamsSelect();
            }
        },
        projectId(newVal) {
            if (this.activeName === 'orderList') {
                this.queryParamsSelect();
            }
        }
    },
    activated() {
        // 保证这里的查询在projectId等变更之后
        setTimeout(() => {
            this.queryParamsSelect();
        }, 0);
    },
    methods: {
        /**
         * 执行查询操作
         * @param {Object} form - 查询表单参数，默认使用this.queryParams
         */
        async handleQuery(form = this.queryParams) {
            if (!this.productLine && this.projectType === '维护') {
                return;
            }
            if (!this.projectId && this.projectType === '开发') {
                return;
            }
            if (this.isQuerying) return;
            this.isQuerying = true;
            let params = this.$tools.cloneDeep(form);

            params = {
                ...params,
                productLine: this.productLine,
                subProductLine: this.subProductLine,
                projectManager: this.projectManager,
                projectId: this.projectId,
                // 流程软硬件归属，固定为硬件
                flowSoftHardBelong: '硬件',
                orderStageType: this.projectType,
                pageSize: this.limit,
                currentPage: this.page
            };
            try {
                const api = this.$service.maintenanceProject.order.getList;
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                const { startRow, total } = res.body;
                this.total = total;
                this.tableData = res.body.list.map((item, index) => {
                    if (startRow) {
                        item.index = startRow + index;
                    }
                    return item;
                });
                this.queryConfig.items.find(
                    (i) => i.name === '产品型号'
                ).elOptions = this.productModelOptions;
                this.queryConfig.items.find(
                    (i) => i.name === '客户名称'
                ).elOptions = this.customerNameOptions;
            } catch (error) {
                console.error(error, 'error');
            } finally {
                this.isQuerying = false;
            }
        },
        /**
         * 根据当前选中的导航项执行查询
         */
        queryParamsSelect() {
            if (this.activeField) {
                const item = this.navItems.find(
                    (i) => i.name === this.activeField
                );
                this.handleNavChange(item);
            } else {
                this.handleQuery();
            }
        },
        /**
         * 跳转到详情页
         * @param  {Object} row 行数据
         */
        handleRoutingJump(row) {
            this.$router.push({
                path:
                    this.projectType === '开发'
                        ? 'projectOrderDetail'
                        : 'maintenanceOrderDetail',
                query: {
                    order_id: row.orderId
                }
            });
        },
        /**
         * 建立禅道任务
         * @param  {Object} row 行数据
         */
        handleCreateTask(row) {
            if (row.proProjectId || !this.showOrderUpdatePermission) return;
            this.rowData = row;
            this.id = row.orderId;
            this.projectManagerAccount = row.projectManager;
            this.targetZentaoTaskDialogVisible = true;
        },
        /**
         * 新建风险
         * @param  {Object} row 行数据
         */
        handleCreateRisk(row) {
            if (row.whetherExistRisk === '是' || !this.showRiskPermission)
                return;
            this.id = row.orderId;
            this.riskUpdateDialogVisible = true;
        },
        /**
         * 编辑订单
         * @param {Object} row - 行数据
         */
        handleOrderEdit(row) {
            if (!this.showOrderUpdatePermission) return;
            this.id = row.orderId;
            this.proTaskId = row.proTaskId;
            this.orderUpdateDialogVisible = true;
        },
        /**
         * 处理导航切换
         * @param {Object} item - 导航项数据
         */
        handleNavChange(item) {
            const form = this.$tools.cloneDeep(oriQureryParams);
            const { queryField, field } = item;
            form[queryField] = field;
            this.handleQuery(form);
        },
        /**
         * 处理搜索按钮点击
         */
        handleSearch() {
            this.activeField = '';
            this.handleQuery();
        },
        /**
         * 处理重置按钮点击
         */
        handleReset() {
            this.activeField = '';
            this.queryParams = this.$tools.cloneDeep(oriQureryParams);
            this.handleQuery();
        },
        /**
         * 创建禅道任务
         * @param {string} value - 项目ID
         */
        async createZentaoTask(value) {
            const api = this.$service.maintenanceProject.order.createZentaoTask;
            const params = {
                ...this.rowData,
                proProjectId: value
            };
            try {
                const res = await api(params);
                if (res.head.code !== '000000') {
                    this.$message.error(res.head.message);
                    return;
                }
                this.$message.success('创建成功');
                this.targetZentaoTaskDialogVisible = false;
                this.queryParamsSelect();
            } catch (error) {
                console.error(error, 'error');
            }
        },
        /**
         * 显示禅道任务列表
         * @param {Object} row - 行数据
         */
        showZentaoTaskList(row) {
            this.projectManagerAccount = row.projectManager;
            this.isShowZentaoTaskList = true;
        },
        /**
         * 隐藏禅道任务列表
         */
        hideZentaoTaskList() {
            this.isShowZentaoTaskList = false;
        },
        getList() {
            this.queryParamsSelect();
        }
    }
};
</script>
<style lang="scss" scoped>
.flex {
    display: flex;
}
.flex-center {
    align-items: center;
    justify-content: center;
}
.gap-5 {
    gap: 5px;
}
.mt-5 {
    margin-top: 5px;
}
.gray {
    color: #bfbfbf;
}
.blue {
    color: #18a8f8;
}
.risk-icon {
    width: 22px;
    height: 22px;
    &:hover {
        cursor: pointer;
        transform: scale(1.2);
    }
}
.task-icon {
    width: 22px;
    height: 22px;
    &:hover {
        cursor: pointer;
        transform: scale(1.2);
    }
}
.edit-icon {
    width: 22px;
    height: 22px;
    color: #18a8f8;
    &:hover {
        cursor: pointer;
        transform: scale(1.2);
    }
}
.overview-ellipsis {
    color: #3370ff;
    word-break: break-all;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    line-clamp: 2;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-align: left;
}
.content-area {
    margin-top: 10px;
    .pagination {
        padding: 0px;
        margin-top: 10px;
    }
}
::v-deep .el-table .el-table__row {
    height: auto !important;
}
::v-deep.el-table th {
    background-color: #3370ff !important;
    padding: 0px !important;
}
::v-deep.el-table th > .cell {
    color: #fff !important;
    padding: 0px !important;
}
::v-deep.el-table th {
    border-right: 1px solid #8c8c8c !important;
}
::v-deep.el-table td {
    border-bottom: 1px solid #dfe6ec !important;
}
</style>
<style>
.maintananceProject-tooltip-width.el-tooltip__popper {
    max-width: 500px;
}
</style>
